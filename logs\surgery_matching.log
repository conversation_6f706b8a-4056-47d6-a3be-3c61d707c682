2025-08-08 12:33:40 - system - INFO - logger.py:95 - ==================================================
2025-08-08 12:33:40 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 12:33:40 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 12:33:40 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 12:33:40 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 12:33:40 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 12:33:40 - system - INFO - logger.py:101 - ==================================================
2025-08-08 12:33:40 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 12:33:54 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '科别': '科别', '姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '术者', '一助': '一助', '二助': '二助', '手术等级': '手术等级', '手术费': '手术费'}
2025-08-08 12:33:54 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 12:33:54 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 50 条，错误 0 条
2025-08-08 12:33:57 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'临床科室': '临床科室', '手术日期': '手术日期', '患者姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '手术医生', '一助': '一助', '二助': '二助', '三助': '三助', '手术费': '手术费'}
2025-08-08 12:33:57 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 12:33:58 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 50 条，错误 0 条
2025-08-08 12:34:58 - src.config.config_manager - INFO - config_manager.py:144 - 配置已保存到: config\app_config.json
2025-08-08 12:34:58 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 12:38:54 - system - INFO - logger.py:95 - ==================================================
2025-08-08 12:38:54 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 12:38:54 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 12:38:54 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 12:38:54 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 12:38:54 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 12:38:54 - system - INFO - logger.py:101 - ==================================================
2025-08-08 12:38:54 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 13:10:52 - system - INFO - logger.py:95 - ==================================================
2025-08-08 13:10:52 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 13:10:52 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 13:10:52 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 13:10:52 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 13:10:52 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 13:10:52 - system - INFO - logger.py:101 - ==================================================
2025-08-08 13:10:53 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 13:11:01 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 13:11:01 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 13:11:02 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 13:11:05 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 13:11:05 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 13:11:05 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 13:11:17 - src.config.config_manager - INFO - config_manager.py:144 - 配置已保存到: config\app_config.json
2025-08-08 13:11:17 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 13:19:51 - system - INFO - logger.py:95 - ==================================================
2025-08-08 13:19:51 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 13:19:51 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 13:19:51 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 13:19:51 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 13:19:51 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 13:19:51 - system - INFO - logger.py:101 - ==================================================
2025-08-08 13:19:51 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 13:36:34 - system - INFO - logger.py:95 - ==================================================
2025-08-08 13:36:34 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 13:36:34 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 13:36:34 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 13:36:34 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 13:36:34 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 13:36:34 - system - INFO - logger.py:101 - ==================================================
2025-08-08 13:36:35 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 13:36:45 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 13:36:45 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 13:36:45 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 13:36:49 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 13:36:49 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 13:36:49 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 13:39:24 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 13:39:24 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 13:44:46 - system - INFO - logger.py:95 - ==================================================
2025-08-08 13:44:46 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 13:44:46 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 13:44:46 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 13:44:46 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 13:44:46 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 13:44:46 - system - INFO - logger.py:101 - ==================================================
2025-08-08 13:44:48 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:08:56 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:08:56 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:08:56 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:08:56 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:08:56 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:08:56 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:08:56 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:08:57 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:09:22 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 14:09:22 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 14:09:35 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:09:35 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:09:35 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:09:35 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:09:35 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:09:35 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:09:35 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:09:36 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:09:48 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 14:09:48 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 14:10:40 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:10:40 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:10:40 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:10:40 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:10:40 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:10:40 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:10:40 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:10:41 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:10:48 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 14:10:48 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 14:10:49 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 14:11:02 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 14:11:02 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 14:12:58 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:12:58 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:12:58 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:12:58 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:12:58 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:12:58 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:12:58 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:13:01 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:13:10 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 14:13:10 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 14:13:10 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 14:13:16 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 14:13:16 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 14:13:16 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 14:13:20 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:13:20 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:13:20 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:13:20 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:13:20 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:13:20 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:13:20 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:13:21 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:13:26 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 14:13:26 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 14:13:27 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 14:13:27 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 14:28:32 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:28:32 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:28:32 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:28:32 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:28:32 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:28:32 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:28:32 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:28:32 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:28:43 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 14:28:43 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 14:28:43 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 14:28:46 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 14:28:46 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 14:28:46 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 14:29:26 - src.services.export_service - INFO - export_service.py:42 - 成功导出匹配结果到: D:/code/OF/手术费用匹配结果.xlsx
2025-08-08 14:29:27 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 14:29:27 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 14:29:59 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:29:59 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:29:59 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:29:59 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:29:59 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:29:59 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:29:59 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:29:59 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 14:30:03 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 14:30:03 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 14:32:58 - system - INFO - logger.py:95 - ==================================================
2025-08-08 14:32:58 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 14:32:58 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 14:32:58 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 14:32:58 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 14:32:58 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 14:32:58 - system - INFO - logger.py:101 - ==================================================
2025-08-08 14:32:59 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:03:51 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:03:51 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:03:51 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:03:51 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:03:51 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:03:51 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:03:51 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:03:51 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:05:04 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:05:04 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:06:20 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:06:20 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:06:20 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:06:20 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:06:20 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:06:20 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:06:20 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:06:20 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:08:05 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:08:05 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:08:45 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:08:45 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:08:45 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:08:45 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:08:45 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:08:45 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:08:45 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:08:45 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:09:00 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:09:00 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:10:11 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:10:11 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:10:11 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:10:11 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:10:11 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:10:11 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:10:11 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:10:11 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:10:31 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:10:31 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:10:31 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:10:34 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:10:34 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:10:34 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:10:43 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:10:43 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:10:49 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:10:49 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:10:49 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:10:49 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:10:49 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:10:49 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:10:49 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:10:49 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:11:17 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:11:17 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:11:22 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:11:22 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:11:22 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:11:22 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:11:22 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:11:22 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:11:22 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:11:23 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:11:45 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:11:45 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:11:55 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:11:55 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:11:55 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:11:55 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:11:55 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:11:55 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:11:55 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:11:56 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:11:59 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:11:59 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:12:56 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:12:56 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:12:56 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:12:56 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:12:56 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:12:56 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:12:56 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:12:56 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:12:59 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:12:59 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:12:59 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:12:59 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:12:59 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:12:59 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:12:59 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:13:00 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:13:08 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:13:08 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:13:08 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:13:11 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:13:12 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:13:12 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:13:30 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:13:30 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:13:31 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:13:31 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:14:46 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:14:46 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:14:46 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:14:46 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:14:46 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:14:46 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:14:46 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:14:46 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:14:51 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:14:51 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:15:48 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:15:48 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:15:48 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:15:48 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:15:48 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:15:48 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:15:48 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:15:48 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:16:21 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:16:21 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:23:16 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:23:16 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:23:16 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:23:16 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:23:16 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:23:16 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:23:16 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:23:17 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:26:43 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:26:43 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:26:43 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:26:43 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:26:43 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:26:43 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:26:43 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:26:43 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:27:21 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:27:21 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:27:21 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:27:41 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:27:41 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:27:41 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:28:11 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:28:11 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:28:35 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:28:35 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:29:06 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:29:06 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:29:06 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:29:06 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:29:06 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:29:06 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:29:06 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:29:06 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:29:59 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:29:59 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:29:59 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:30:07 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:30:07 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:30:07 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:30:33 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:30:33 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:30:33 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:30:33 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:30:33 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:30:33 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:30:33 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:30:34 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:30:36 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:30:36 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:30:46 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:30:46 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:30:46 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:30:46 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:30:46 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:30:46 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:30:46 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:30:46 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:30:56 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:30:56 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:30:56 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:33:05 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:33:05 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:34:36 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:34:36 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:34:36 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:34:36 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:34:36 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:34:36 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:34:36 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:34:36 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:34:48 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:34:48 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:34:48 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:34:56 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:34:56 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:34:56 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:36:58 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:36:58 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:38:23 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:38:23 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:38:23 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:38:23 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:38:23 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:38:23 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:38:23 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:38:23 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:39:38 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:39:38 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:39:38 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:39:47 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:39:47 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:42:15 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:42:15 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:42:15 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:42:15 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:42:15 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:42:15 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:42:15 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:42:15 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:42:32 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:42:32 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:43:56 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:43:56 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:43:56 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:43:56 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:43:56 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:43:56 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:43:56 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:43:56 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:44:07 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:44:07 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:44:19 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:44:19 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:44:19 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:44:19 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:44:19 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:44:19 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:44:19 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:44:19 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:44:23 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:44:23 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:44:26 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:44:26 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:44:26 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:44:26 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:44:26 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:44:26 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:44:26 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:44:27 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:44:35 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:44:35 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:44:35 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:44:50 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:44:50 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:45:07 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:45:07 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:45:07 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:45:07 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:45:07 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:45:07 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:45:07 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:45:08 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:45:09 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:45:09 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:46:00 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:46:00 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:46:00 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:46:00 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:46:00 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:46:00 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:46:00 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:46:00 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:46:10 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:46:10 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:46:10 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:46:31 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:46:31 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:46:31 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:46:31 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:46:31 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:46:31 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:46:31 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:46:31 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:46:44 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:46:44 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:47:06 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:47:06 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:47:20 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:47:20 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:47:20 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:47:20 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:47:20 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:47:20 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:47:20 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:47:21 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:47:28 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '科别': '科别', '姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '术者', '一助': '一助', '二助': '二助', '手术等级': '手术等级', '手术费': '手术费'}
2025-08-08 15:47:28 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:47:28 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 408 条，错误 0 条
2025-08-08 15:47:38 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:47:38 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:48:42 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:48:42 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:48:42 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:48:42 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:48:42 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:48:42 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:48:42 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:48:43 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:49:00 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:49:00 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:49:00 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:49:00 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:49:00 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:49:00 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:49:00 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:49:00 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:49:16 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:49:16 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:49:20 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:49:20 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:49:40 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:49:40 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:49:40 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:49:40 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:49:40 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:49:40 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:49:40 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:49:41 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:49:47 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:49:47 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:49:48 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:49:59 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:49:59 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:49:59 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:50:03 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:50:03 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:50:55 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:50:55 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:50:55 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:50:55 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:50:55 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:50:55 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:50:55 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:50:56 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:51:01 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:51:01 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:51:02 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:51:06 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:51:06 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:51:06 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:51:06 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:51:06 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:51:06 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:51:06 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:51:06 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:52:09 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:52:09 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:52:12 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:52:12 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:52:12 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:52:12 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:52:12 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:52:12 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:52:12 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:52:12 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:52:19 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:52:19 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:52:19 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:52:24 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:52:24 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:52:24 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:52:32 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:52:32 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:52:32 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:52:32 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:52:32 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:52:32 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:52:32 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:52:32 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:52:36 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:52:36 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:53:49 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:53:49 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:54:08 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:54:08 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:54:08 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:54:08 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:54:08 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:54:08 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:54:08 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:54:08 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:54:16 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:54:16 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:54:16 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:54:18 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:54:18 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:54:18 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:54:31 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:54:31 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:54:44 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:54:44 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:54:44 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:54:44 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:54:44 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:54:44 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:54:44 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:54:44 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:54:53 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:54:53 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:54:53 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:55:02 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:55:02 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:56:41 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:56:41 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:56:41 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:56:41 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:56:41 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:56:41 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:56:41 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:56:41 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:56:59 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:56:59 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:57:00 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:57:04 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:57:04 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:57:04 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:59:13 - system - INFO - logger.py:95 - ==================================================
2025-08-08 15:59:13 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 15:59:13 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 15:59:13 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 15:59:13 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 15:59:13 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 15:59:13 - system - INFO - logger.py:101 - ==================================================
2025-08-08 15:59:13 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 15:59:16 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:59:16 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 15:59:24 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:59:24 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:59:24 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 15:59:34 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 15:59:34 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 15:59:34 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 15:59:42 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 15:59:42 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:01:38 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:01:38 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:01:38 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:01:38 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:01:38 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:01:38 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:01:38 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:01:39 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:01:59 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:01:59 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:01:59 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:02:03 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:02:03 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:02:03 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:03:13 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:03:13 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:05:15 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:05:15 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:05:15 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:05:15 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:05:15 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:05:15 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:05:15 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:05:16 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:05:32 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:05:32 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:05:33 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:05:38 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:05:38 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:05:38 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:05:49 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:05:49 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:06:06 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:06:06 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:06:06 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:06:06 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:06:06 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:06:06 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:06:06 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:06:07 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:06:10 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:06:10 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:06:50 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:06:50 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:06:50 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:06:50 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:06:50 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:06:50 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:06:50 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:06:50 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:06:55 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:06:55 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:07:44 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:07:44 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:07:44 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:07:44 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:07:44 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:07:44 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:07:44 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:07:45 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:07:48 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:07:48 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:09:17 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:09:17 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:09:17 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:09:17 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:09:17 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:09:17 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:09:17 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:09:17 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:09:39 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:09:39 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:11:51 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:11:51 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:11:51 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:11:51 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:11:51 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:11:51 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:11:51 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:11:52 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:11:59 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:11:59 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:12:12 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:12:12 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:12:12 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:12:12 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:12:12 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:12:12 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:12:12 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:12:13 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:12:17 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:12:17 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:12:25 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:12:25 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:12:25 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:12:25 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:12:25 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:12:25 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:12:25 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:12:25 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:12:30 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:12:30 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:13:05 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:13:05 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:13:05 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:13:05 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:13:05 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:13:05 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:13:05 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:13:05 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:13:09 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:13:09 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:15:49 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:15:49 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:15:49 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:15:49 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:15:49 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:15:49 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:15:49 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:15:49 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:16:12 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:16:12 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:16:12 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:16:50 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:16:50 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:19:42 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:19:42 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:19:42 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:19:42 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:19:42 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:19:42 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:19:42 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:19:42 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:19:59 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:19:59 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:19:59 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:20:06 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:20:06 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:20:06 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:20:10 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:20:10 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:20:10 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:20:10 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:20:10 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:20:10 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:20:10 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:20:11 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:20:12 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:20:12 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:20:34 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:20:34 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:20:41 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:20:41 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:20:41 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:20:41 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:20:41 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:20:41 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:20:41 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:20:41 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:21:09 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:21:09 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:21:09 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:21:15 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:21:15 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:21:15 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:21:21 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:21:21 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:21:57 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:21:57 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:21:57 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:21:57 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:21:57 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:21:57 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:21:57 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:21:57 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:22:15 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:22:15 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:22:15 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:22:25 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:22:25 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:24:40 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:24:40 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:24:40 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:24:40 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:24:40 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:24:40 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:24:40 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:24:40 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:25:32 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:25:32 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:25:32 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:25:55 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:25:55 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:26:31 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:26:31 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:26:31 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:26:31 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:26:31 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:26:31 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:26:31 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:26:31 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:26:42 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:26:42 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:29:23 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:29:23 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:29:23 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:29:23 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:29:23 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:29:23 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:29:23 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:29:23 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:29:32 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:29:32 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:29:32 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:29:37 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:29:37 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:29:37 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:29:40 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:29:40 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:29:40 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:29:40 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:29:40 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:29:40 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:29:40 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:29:41 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:29:43 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:29:43 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:29:49 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:29:49 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:34:07 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:34:07 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:34:07 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:34:07 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:34:07 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:34:07 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:34:07 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:34:07 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:34:19 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:34:19 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:34:19 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:34:26 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:34:26 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:34:27 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:35:07 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:35:07 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:37:40 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:37:40 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:37:40 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:37:40 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:37:40 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:37:40 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:37:40 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:37:40 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:37:48 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:37:48 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:37:49 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:37:59 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:37:59 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:37:59 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:38:00 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:38:00 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:38:14 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:38:14 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:38:14 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:38:14 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:38:14 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:38:14 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:38:14 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:38:14 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:39:09 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:39:09 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:41:11 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:41:11 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:41:11 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:41:11 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:41:11 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:41:11 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:41:11 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:41:11 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:41:46 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:41:46 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:42:00 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:42:00 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:42:00 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:42:00 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:42:00 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:42:00 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:42:00 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:42:00 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:42:48 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:42:48 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:43:17 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:43:17 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:43:17 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:43:17 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:43:17 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:43:17 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:43:17 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:43:17 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:43:30 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:43:30 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:43:30 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:43:30 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:43:30 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:43:30 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:43:30 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:43:30 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:43:32 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:43:32 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:43:38 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:43:38 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:43:39 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:43:39 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:43:39 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:43:39 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:43:39 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:43:39 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:43:39 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:43:39 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:43:50 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:43:50 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:45:06 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:45:06 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:45:06 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:45:06 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:45:06 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:45:06 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:45:06 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:45:06 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:45:54 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:45:54 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:46:26 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:46:26 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:46:26 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:46:26 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:46:26 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:46:26 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:46:26 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:46:26 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:46:35 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:46:35 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:49:12 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:49:12 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:49:12 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:49:12 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:49:12 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:49:12 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:49:12 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:49:12 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:51:20 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:51:20 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:53:03 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:53:03 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:53:03 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:53:03 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:53:03 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:53:03 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:53:03 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:53:03 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:53:47 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:53:47 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:54:19 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:54:19 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:54:19 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:54:19 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:54:19 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:54:19 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:54:19 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:54:20 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:54:27 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:54:27 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:54:31 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:54:31 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:54:31 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:54:31 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:54:31 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:54:31 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:54:31 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:54:32 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:54:39 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:54:39 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:54:39 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:54:41 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:54:41 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:54:42 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:55:17 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:55:17 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 16:56:44 - system - INFO - logger.py:95 - ==================================================
2025-08-08 16:56:44 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 16:56:44 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 16:56:44 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 16:56:44 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 16:56:44 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 16:56:44 - system - INFO - logger.py:101 - ==================================================
2025-08-08 16:56:44 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 16:56:52 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:56:52 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:56:52 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 16:56:54 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 16:56:54 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 16:56:54 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 16:57:18 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 16:57:18 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 17:01:43 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:01:43 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:01:43 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:01:43 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:01:43 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:01:43 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:01:43 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:01:43 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 17:01:50 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:01:50 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 17:01:50 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 17:01:53 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:01:53 - src.services.data_import_service - INFO - data_import_service.py:291 - 数据清洗完成
2025-08-08 17:01:53 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 17:05:51 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:05:51 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:05:51 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:05:51 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:05:51 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:05:51 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:05:51 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:05:51 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 17:05:57 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:05:57 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:05:57 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 17:06:00 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:06:00 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:06:00 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 17:07:16 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:07:16 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:07:16 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:07:16 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:07:16 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:07:16 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:07:16 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:07:17 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 17:07:23 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:07:23 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:07:23 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 17:07:26 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:07:26 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:07:26 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 17:07:43 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:07:43 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 17:10:17 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:10:17 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:10:17 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:10:17 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:10:17 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:10:17 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:10:17 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:10:17 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 17:10:24 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:10:24 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:10:24 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 17:10:26 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:10:26 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:10:26 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 17:11:00 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:11:00 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 17:11:53 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:11:53 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:11:53 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:11:53 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:11:53 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:11:53 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:11:53 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:11:54 - main - INFO - main.py:90 - 应用程序启动成功
2025-08-08 17:12:01 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:12:01 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:12:01 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 17:12:03 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:12:03 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:12:03 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 17:12:18 - src.services.export_service - INFO - export_service.py:42 - 成功导出匹配结果到: C:/Users/<USER>/Desktop/手术费/手术费用匹配结果.xlsx
2025-08-08 17:12:20 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:12:20 - main - INFO - main.py:109 - 应用程序退出，退出代码: 0
2025-08-08 17:18:12 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:18:12 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:18:12 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:18:12 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:18:12 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:18:12 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:18:12 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:18:13 - main - INFO - main.py:95 - 应用程序启动成功
2025-08-08 17:18:17 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:18:17 - main - INFO - main.py:114 - 应用程序退出，退出代码: 0
2025-08-08 17:19:51 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:19:51 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:19:51 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:19:51 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:19:51 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:19:51 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:19:51 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:19:52 - main - INFO - main.py:95 - 应用程序启动成功
2025-08-08 17:19:59 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:19:59 - main - INFO - main.py:114 - 应用程序退出，退出代码: 0
2025-08-08 17:25:27 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:25:27 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:25:27 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:25:27 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:25:27 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:25:27 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:25:27 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:25:27 - main - INFO - main.py:95 - 应用程序启动成功
2025-08-08 17:25:30 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:25:30 - main - INFO - main.py:114 - 应用程序退出，退出代码: 0
2025-08-08 17:29:35 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:29:35 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:29:35 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:29:35 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:29:35 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:29:35 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:29:35 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:29:36 - main - INFO - main.py:96 - 应用程序启动成功
2025-08-08 17:29:43 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:29:43 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:29:43 - src.services.data_import_service - INFO - data_import_service.py:95 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 17:29:46 - src.services.data_import_service - INFO - data_import_service.py:260 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:29:46 - src.services.data_import_service - INFO - data_import_service.py:295 - 数据清洗完成
2025-08-08 17:29:46 - src.services.data_import_service - INFO - data_import_service.py:139 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 17:29:53 - src.services.export_service - INFO - export_service.py:42 - 成功导出匹配结果到: D:/code/OF/手术费用匹配结果.xlsx
2025-08-08 17:29:57 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:29:57 - main - INFO - main.py:115 - 应用程序退出，退出代码: 0
2025-08-08 17:34:57 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:34:57 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:34:57 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:34:57 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:34:57 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:34:57 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:34:57 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:34:57 - main - INFO - main.py:96 - 应用程序启动成功
2025-08-08 17:35:08 - src.services.data_import_service - INFO - data_import_service.py:268 - 字段映射成功: {'手术日期': '手术日期', '临床科室': '科别', '患者姓名': '姓名', '住院号': '住院号', '手术名称': '手术名称', '手术医生': '术者', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:35:08 - src.services.data_import_service - INFO - data_import_service.py:303 - 数据清洗完成
2025-08-08 17:35:08 - src.services.data_import_service - INFO - data_import_service.py:303 - 数据清洗完成
2025-08-08 17:35:08 - src.services.data_import_service - INFO - data_import_service.py:101 - 成功导入表一数据 487 条，错误 0 条
2025-08-08 17:35:10 - src.services.data_import_service - INFO - data_import_service.py:268 - 字段映射成功: {'科别': '临床科室', '手术日期': '手术日期', '姓名': '患者姓名', '住院号': '住院号', '手术名称': '手术名称', '术者': '手术医生', '一助': '一助', '二助': '二助', '手术费': '手术费'}
2025-08-08 17:35:10 - src.services.data_import_service - INFO - data_import_service.py:303 - 数据清洗完成
2025-08-08 17:35:10 - src.services.data_import_service - INFO - data_import_service.py:147 - 成功导入表二数据 408 条，错误 0 条
2025-08-08 17:35:19 - src.services.export_service - INFO - export_service.py:42 - 成功导出匹配结果到: D:/code/OF/手术费用匹配结果.xlsx
2025-08-08 17:35:21 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:35:21 - main - INFO - main.py:115 - 应用程序退出，退出代码: 0
2025-08-08 17:41:27 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:41:27 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:41:27 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:41:27 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:41:27 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:41:27 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:41:27 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:41:27 - main - INFO - main.py:96 - 应用程序启动成功
2025-08-08 17:41:30 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:41:30 - main - INFO - main.py:115 - 应用程序退出，退出代码: 0
2025-08-08 17:50:03 - system - INFO - logger.py:95 - ==================================================
2025-08-08 17:50:03 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-08 17:50:03 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-08 17:50:03 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-08 17:50:03 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-08 17:50:03 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-08 17:50:03 - system - INFO - logger.py:101 - ==================================================
2025-08-08 17:50:04 - main - INFO - main.py:96 - 应用程序启动成功
2025-08-08 17:50:06 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-08 17:50:06 - main - INFO - main.py:115 - 应用程序退出，退出代码: 0
2025-08-10 08:16:49 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:16:49 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:16:49 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:16:49 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:16:49 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:16:49 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:16:49 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:16:49 - main - INFO - main.py:123 - 应用程序启动成功
2025-08-10 08:16:59 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 08:16:59 - main - INFO - main.py:142 - 应用程序退出，退出代码: 0
2025-08-10 08:29:15 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:29:15 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:29:15 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:29:15 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:29:15 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:29:15 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:29:15 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:31:26 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:31:26 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:31:26 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:31:26 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:31:26 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:31:26 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:31:26 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:31:39 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:31:39 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:31:39 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:31:39 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:31:39 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:31:39 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:31:39 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:31:39 - main - INFO - main.py:329 - 应用程序启动成功
2025-08-10 08:31:44 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 08:31:44 - config - ERROR - logger.py:107 - 异常发生在 配置操作 应用启动: name 'logger' is not defined
Traceback (most recent call last):
  File "d:\code\OF\main.py", line 360, in main
    logger.info(f"应用程序退出，退出代码: {exit_code}")
NameError: name 'logger' is not defined
2025-08-10 08:33:35 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:33:35 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:33:35 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:33:35 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:33:35 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:33:35 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:33:35 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:48:10 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:48:10 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:48:10 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:48:10 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:48:10 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:48:10 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:48:10 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:48:10 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 08:48:10 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 08:48:11 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 08:48:11 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.79秒
2025-08-10 08:48:24 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 08:48:24 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 08:48:24 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 08:50:43 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:50:43 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:50:43 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:50:43 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:50:43 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:50:43 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:50:43 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:50:43 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 08:50:43 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 08:50:44 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 08:50:44 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.70秒
2025-08-10 08:51:05 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 08:51:05 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 08:51:05 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 08:55:07 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:55:07 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:55:07 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:55:07 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:55:07 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:55:07 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:55:07 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:55:07 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 08:55:07 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 08:55:08 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 08:55:08 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.75秒
2025-08-10 08:55:47 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 08:55:47 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 08:55:47 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 08:56:58 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:56:58 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:56:58 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:56:58 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:56:58 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:56:58 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:56:58 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:56:58 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 08:56:58 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 08:56:58 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 08:56:58 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.56秒
2025-08-10 08:57:11 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 08:57:11 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 08:57:11 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 08:58:01 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:58:01 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:58:01 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:58:01 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:58:01 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:58:01 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:58:01 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:58:01 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 08:58:01 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 08:58:02 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 08:58:02 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.74秒
2025-08-10 08:58:10 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 08:58:10 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 08:58:10 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 08:59:39 - system - INFO - logger.py:95 - ==================================================
2025-08-10 08:59:39 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 08:59:39 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 08:59:39 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 08:59:39 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 08:59:39 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 08:59:39 - system - INFO - logger.py:101 - ==================================================
2025-08-10 08:59:39 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 08:59:39 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 08:59:40 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 08:59:40 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.73秒
2025-08-10 09:00:28 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 09:00:28 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 09:00:28 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 09:00:40 - system - INFO - logger.py:95 - ==================================================
2025-08-10 09:00:40 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 09:00:40 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 09:00:40 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 09:00:40 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 09:00:40 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 09:00:40 - system - INFO - logger.py:101 - ==================================================
2025-08-10 09:00:40 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 09:00:40 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 09:00:40 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 09:00:40 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.62秒
2025-08-10 09:00:51 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 09:00:51 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 09:00:51 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 09:09:50 - system - INFO - logger.py:95 - ==================================================
2025-08-10 09:09:50 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 09:09:50 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 09:09:50 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 09:09:50 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 09:09:50 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 09:09:50 - system - INFO - logger.py:101 - ==================================================
2025-08-10 09:09:50 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 09:09:50 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 09:09:51 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 09:09:51 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.51秒
2025-08-10 09:10:00 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 09:10:00 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 09:10:00 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 09:11:30 - system - INFO - logger.py:95 - ==================================================
2025-08-10 09:11:30 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 09:11:30 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 09:11:30 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 09:11:30 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 09:11:30 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 09:11:30 - system - INFO - logger.py:101 - ==================================================
2025-08-10 09:11:30 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 09:11:30 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 09:11:31 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 09:11:31 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.67秒
2025-08-10 09:11:34 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 09:11:34 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 09:11:34 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 09:16:08 - system - INFO - logger.py:95 - ==================================================
2025-08-10 09:16:08 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 09:16:08 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 09:16:08 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 09:16:08 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 09:16:08 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 09:16:08 - system - INFO - logger.py:101 - ==================================================
2025-08-10 09:16:08 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 09:16:08 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 09:16:08 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 09:16:08 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.64秒
2025-08-10 09:16:38 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 09:16:38 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 09:16:38 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
2025-08-10 12:47:02 - system - INFO - logger.py:95 - ==================================================
2025-08-10 12:47:02 - system - INFO - logger.py:96 - 应用启动: surgery_matching
2025-08-10 12:47:02 - system - INFO - logger.py:97 - Python版本: 3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit (AMD64)]
2025-08-10 12:47:02 - system - INFO - logger.py:98 - 工作目录: D:\code\OF
2025-08-10 12:47:02 - system - INFO - logger.py:99 - 日志级别: INFO
2025-08-10 12:47:02 - system - INFO - logger.py:100 - 日志目录: D:\code\OF\logs
2025-08-10 12:47:02 - system - INFO - logger.py:101 - ==================================================
2025-08-10 12:47:02 - launcher - INFO - app_launcher.py:53 - 应用程序启动器初始化完成
2025-08-10 12:47:02 - launcher - INFO - app_launcher.py:62 - 应用程序配置加载完成
2025-08-10 12:47:02 - launcher - INFO - app_launcher.py:82 - 主窗口创建成功
2025-08-10 12:47:02 - launcher - INFO - fast_launcher.py:314 - 快速启动完成，耗时: 1.80秒
2025-08-10 12:50:06 - src.config.config_manager - INFO - config_manager.py:143 - 配置已保存到: config\app_config.json
2025-08-10 12:50:06 - launcher - INFO - app_launcher.py:198 - 窗口配置保存成功
2025-08-10 12:50:06 - launcher - INFO - app_launcher.py:201 - 应用程序退出，退出代码: 0
