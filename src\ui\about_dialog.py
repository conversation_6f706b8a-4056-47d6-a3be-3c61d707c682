#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
关于对话框
显示应用程序信息、版本、开发者等信息
"""
import sys
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel,
    QPushButton, QTabWidget, QWidget,
    QScrollArea, QFrame
)
from PySide6.QtCore import Qt, QUrl
from PySide6.QtGui import QPixmap, QIcon, QFont, QDesktopServices

from .styles import ModernStyle
from .content_manager import ContentManager


class AboutDialog(QDialog):
    """关于对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("关于 手术费用匹配系统")
        self.resize(600, 500)  # 使用resize而不是setFixedSize
        self.setMinimumSize(500, 400)  # 设置最小尺寸
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # 设置图标
        self._set_window_icon()
        
        # 设置样式
        self.setStyleSheet(ModernStyle.get_dialog_style())
        
        # 创建界面
        self._create_ui()
        
        # 居中显示
        self._center_on_parent()

        # 保存窗口状态
        self._load_window_state()
    
    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取图标路径
            if getattr(sys, 'frozen', False):
                base_path = Path(sys._MEIPASS)
            else:
                base_path = Path(__file__).parent.parent.parent
            
            icon_paths = [
                base_path / "src" / "logo" / "logo.ico",
                base_path / "src" / "logo" / "logo.png",
                Path("src") / "logo" / "logo.ico",
                Path("src") / "logo" / "logo.png",
            ]
            
            for icon_path in icon_paths:
                if icon_path.exists():
                    self.setWindowIcon(QIcon(str(icon_path)))
                    return
        except Exception:
            pass
    
    def _create_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建标签页
        tab_widget = QTabWidget()
        tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: none;
                background: white;
            }
            QTabBar::tab {
                background: #f0f0f0;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                min-width: 80px;
            }
            QTabBar::tab:selected {
                background: white;
                color: #2196F3;
                font-weight: bold;
            }
            QTabBar::tab:hover {
                background: #e3f2fd;
            }
        """)
        
        # 添加标签页
        tab_widget.addTab(self._create_about_tab(), "关于")
        tab_widget.addTab(self._create_contact_tab(), "联系方式")
        
        layout.addWidget(tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #2196F3;
                color: white;
                border: none;
                padding: 10px 30px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #1976D2;
            }
            QPushButton:pressed {
                background: #1565C0;
            }
        """)
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(close_btn)
        button_layout.setContentsMargins(20, 10, 20, 20)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def _create_about_tab(self):
        """创建关于标签页"""
        widget = QWidget()

        # 创建滚动区域以支持小窗口
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; }")

        content_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 应用图标和标题
        header_layout = QHBoxLayout()
        
        # 图标
        icon_label = QLabel()
        icon_label.setFixedSize(80, 80)
        icon_label.setStyleSheet("""
            QLabel {
                background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
                    stop:0 #2196F3, stop:1 #1976D2);
                border-radius: 40px;
                border: 3px solid white;
            }
        """)
        
        # 尝试加载图标
        try:
            if getattr(sys, 'frozen', False):
                base_path = Path(sys._MEIPASS)
            else:
                base_path = Path(__file__).parent.parent.parent
            
            icon_paths = [
                base_path / "src" / "logo" / "logo.png",
                Path("src") / "logo" / "logo.png",
            ]
            
            for icon_path in icon_paths:
                if icon_path.exists():
                    pixmap = QPixmap(str(icon_path))
                    if not pixmap.isNull():
                        scaled_pixmap = pixmap.scaled(74, 74, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                        icon_label.setPixmap(scaled_pixmap)
                        icon_label.setAlignment(Qt.AlignCenter)
                        break
        except Exception:
            # 如果无法加载图标，显示医疗十字
            icon_label.setText("✚")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setStyleSheet(icon_label.styleSheet() + """
                color: white;
                font-size: 36px;
                font-weight: bold;
            """)
        
        # 标题信息
        title_layout = QVBoxLayout()
        title_layout.setSpacing(5)
        
        app_name = QLabel(ContentManager.APP_NAME)
        app_name.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #1976D2;
            }
        """)

        app_subtitle = QLabel(ContentManager.APP_NAME_EN)
        app_subtitle.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                font-style: italic;
            }
        """)

        version_label = QLabel(f"版本 {ContentManager.APP_VERSION}")
        version_label.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #888;
                background: #f5f5f5;
                padding: 4px 8px;
                border-radius: 4px;
            }
        """)
        
        title_layout.addWidget(app_name)
        title_layout.addWidget(app_subtitle)
        title_layout.addWidget(version_label)
        title_layout.addStretch()
        
        header_layout.addWidget(icon_label)
        header_layout.addSpacing(20)
        header_layout.addLayout(title_layout)
        header_layout.addStretch()
        
        layout.addLayout(header_layout)
        
        # 分隔线
        line = QFrame()
        line.setFrameShape(QFrame.HLine)
        line.setStyleSheet("QFrame { color: #e0e0e0; }")
        layout.addWidget(line)
        
        # 应用描述
        description_text = ContentManager.get_app_description_full()
        features = ContentManager.get_key_features()
        features_html = "<br>".join([f"• {feature}" for feature in features])

        full_description = f"""
{description_text}<br>
<b>核心特性：</b><br>
{features_html}
        """

        description = QLabel(full_description)
        description.setWordWrap(True)
        description.setStyleSheet("""
            QLabel {
                font-size: 13px;
                line-height: 1.6;
                color: #333;
                background: #f9f9f9;
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid #2196F3;
            }
        """)
        
        layout.addWidget(description)

        # 系统要求
        requirements = QLabel(ContentManager.get_system_requirements_html())
        requirements.setStyleSheet("""
            QLabel {
                font-size: 12px;
                color: #555;
                background: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                border-left: 4px solid #28a745;
            }
        """)
        layout.addWidget(requirements)

        layout.addStretch()

        # 设置内容到滚动区域
        content_widget.setLayout(layout)
        scroll.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll)

        widget.setLayout(main_layout)
        return widget
    

    

    

    
    def _create_contact_tab(self):
        """创建联系方式标签页"""
        widget = QWidget()

        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; }")

        content_widget = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(30, 30, 30, 30)
        layout.setSpacing(20)
        
        # 开发团队信息
        team_info = QLabel("""
<h2 style="color: #1976D2; text-align: center;">开发团队</h2>
<p style="text-align: center; font-size: 14px; color: #666; margin-bottom: 30px;">
致力于为医疗行业提供优质的数据处理解决方案
</p>
        """)
        team_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(team_info)
        
        # 联系信息
        contact_layout = QVBoxLayout()
        contact_layout.setSpacing(15)
        
        contacts = ContentManager.CONTACT_INFO
        
        for icon, label, value, link in contacts:
            contact_item = self._create_contact_item(icon, label, value, link)
            contact_layout.addWidget(contact_item)
        
        layout.addLayout(contact_layout)
        
        # 版权信息
        copyright_info = QLabel(f"""
<hr style="border: none; border-top: 1px solid #e0e0e0; margin: 30px 0;">
<p style="text-align: center; font-size: 12px; color: #888;">
{ContentManager.TEAM_INFO['copyright']}<br>
{ContentManager.TEAM_INFO['usage_note']}
</p>
        """)
        copyright_info.setAlignment(Qt.AlignCenter)
        layout.addWidget(copyright_info)

        layout.addStretch()

        # 设置内容到滚动区域
        content_widget.setLayout(layout)
        scroll.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.addWidget(scroll)

        widget.setLayout(main_layout)
        return widget
    
    def _create_contact_item(self, icon, label, value, link=None):
        """创建联系项目"""
        widget = QWidget()
        widget.setStyleSheet("""
            QWidget {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
            }
            QWidget:hover {
                background: #e3f2fd;
            }
        """)
        
        layout = QHBoxLayout()
        layout.setSpacing(15)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                color: #2196F3;
                background: white;
                border-radius: 20px;
                padding: 8px;
                min-width: 20px;
                max-width: 20px;
                text-align: center;
            }
        """)
        icon_label.setAlignment(Qt.AlignCenter)
        
        # 内容
        content_layout = QVBoxLayout()
        content_layout.setSpacing(2)
        
        label_widget = QLabel(label)
        label_widget.setStyleSheet("""
            QLabel {
                font-weight: bold;
                color: #333;
                font-size: 14px;
                background: none;
            }
        """)
        
        value_widget = QLabel(value)
        if link:
            value_widget.setStyleSheet("""
                QLabel {
                    color: #2196F3;
                    font-size: 13px;
                    background: none;
                }
                QLabel:hover {
                    text-decoration: underline;
                }
            """)
            value_widget.setCursor(Qt.PointingHandCursor)
            value_widget.mousePressEvent = lambda _: QDesktopServices.openUrl(QUrl(link))
        else:
            value_widget.setStyleSheet("""
                QLabel {
                    color: #666;
                    font-size: 13px;
                    background: none;
                }
            """)
        
        content_layout.addWidget(label_widget)
        content_layout.addWidget(value_widget)
        
        layout.addWidget(icon_label)
        layout.addLayout(content_layout)
        layout.addStretch()
        
        widget.setLayout(layout)
        return widget
    
    def _center_on_parent(self):
        """在父窗口中居中显示"""
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
            self.move(x, y)
        else:
            # 如果没有父窗口，在屏幕中央显示
            from PySide6.QtGui import QGuiApplication
            screen = QGuiApplication.primaryScreen()
            if screen:
                screen_rect = screen.geometry()
                x = (screen_rect.width() - self.width()) // 2
                y = (screen_rect.height() - self.height()) // 2
                self.move(x, y)

    def _load_window_state(self):
        """加载窗口状态"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("SurgeryMatching", "AboutDialog")

            # 恢复窗口大小
            size = settings.value("size")
            if size:
                self.resize(size)

            # 恢复窗口位置
            pos = settings.value("pos")
            if pos:
                self.move(pos)
        except Exception:
            pass  # 如果加载失败，使用默认设置

    def _save_window_state(self):
        """保存窗口状态"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("SurgeryMatching", "AboutDialog")
            settings.setValue("size", self.size())
            settings.setValue("pos", self.pos())
        except Exception:
            pass  # 如果保存失败，忽略错误

    def closeEvent(self, event):
        """窗口关闭事件"""
        self._save_window_state()
        super().closeEvent(event)
