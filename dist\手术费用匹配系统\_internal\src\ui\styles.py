"""
UI样式定义模块
包含所有界面组件的样式定义
"""


class ModernStyle:
    """现代化样式定义"""

    @staticmethod
    def get_main_style():
        """获取主界面样式"""
        return """
        QMainWindow {
            background-color: #f8f9fa;
            color: #212529;
        }

        QTabWidget::pane {
            border: 1px solid #dee2e6;
            background-color: white;
            border-radius: 12px;
            margin-top: 5px;
        }

        QTabWidget::tab-bar {
            alignment: left;
        }

        QTabBar::tab {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 8px 16px;
            margin-right: 2px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            color: #6c757d;
            min-width: 60px;
        }
        
        QTabBar::tab:selected {
            background-color: #0d6efd;
            border: 1px solid #0d6efd;
            color: white;
            font-weight: 600;
        }

        QGroupBox {
            font-weight: 600;
            font-size: 15px;
            border: 2px solid #dee2e6;
            border-radius: 12px;
            margin-top: 15px;
            padding-top: 20px;
            background-color: white;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 20px;
            padding: 0 10px 0 10px;
            color: #495057;
            background-color: white;
        }

        QPushButton {
            background-color: #0d6efd;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 14px;
            min-height: 20px;
        }

        QPushButton:pressed {
            background-color: #0a58ca;
        }

        QPushButton:disabled {
            background-color: #6c757d;
            color: #adb5bd;
        }
        
        QLineEdit, QSpinBox {
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 10px 15px;
            font-size: 14px;
            background-color: white;
            color: #495057;
        }

        QLineEdit:focus, QSpinBox:focus {
            border-color: #0d6efd;
            outline: none;
            box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
        }

        QCheckBox {
            font-size: 14px;
            color: #495057;
            spacing: 8px;
        }

        QCheckBox::indicator {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            border: 2px solid #dee2e6;
            background-color: white;
        }

        QCheckBox::indicator:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
            image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xMC42IDEuNEw0LjIgNy44TDEuNCA1IiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
        }

        QTableWidget {
            gridline-color: #dee2e6;
            background-color: white;
            alternate-background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            selection-background-color: #e7f3ff;
        }

        QTableWidget::item {
            padding: 8px;
            border-bottom: 1px solid #f1f3f4;
        }

        QTableWidget::item:selected {
            background-color: #0078d4;
            color: white;
        }
        
        QHeaderView::section {
            background-color: #f0f0f0;
            padding: 6px;
            border: 1px solid #cccccc;
            font-weight: bold;
        }
        """

    @staticmethod
    def get_import_card_style(bg_color: str, accent_color: str) -> str:
        """获取导入卡片样式"""
        return f"""
            QWidget {{
                background-color: {bg_color};
                border-radius: 12px;
                padding: 15px;
            }}
            QPushButton {{
                background-color: {accent_color};
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 8px;
                font-weight: 600;
                font-size: 14px;
            }}
            QPushButton:disabled {{
                background-color: #6c757d;
                color: #adb5bd;
            }}
            QLabel {{
                color: {accent_color};
                font-weight: 600;
            }}
        """

    @staticmethod
    def get_stat_card_style(color: str) -> str:
        """获取统计卡片样式"""
        return f"""
            QWidget {{
                background-color: white;
                border: 1px solid {color};
                border-radius: 4px;
                padding: 2px;
            }}
            QLabel {{
                color: {color};
                border: none;
            }}
        """

    @staticmethod
    def get_status_bar_style() -> str:
        """获取状态栏样式"""
        return """
            QStatusBar {
                background-color: #f8f9fa;
                border-top: 1px solid #dee2e6;
                padding: 8px;
                font-size: 13px;
                color: #495057;
            }
        """

    @staticmethod
    def get_dialog_style():
        """获取对话框样式"""
        return """
            QDialog {
                background-color: #f8f9fa;
                font-family: "Microsoft YaHei";
            }
            QLabel {
                color: #333;
                font-size: 13px;
            }
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
            QPushButton:pressed {
                background-color: #1565C0;
            }
        """
