(['d:\\code\\OF\\main.py'],
 ['d:\\code\\OF'],
 ['PySide6.QtCore',
  'PySide6.QtGui',
  'PySide6.QtWidgets',
  'pandas',
  'openpyxl',
  'rapidfuzz',
  'fuzzywuzzy'],
 [('D:\\code\\OF\\venv\\Lib\\site-packages\\numpy\\_pyinstaller', 0),
  ('D:\\code\\OF\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('D:\\code\\OF\\venv\\lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 ['tkinter',
  'matplotlib',
  'scipy',
  'IPython',
  'jupyter',
  'pytest',
  'numpy.testing',
  '__main__'],
 [],
 False,
 {},
 2,
 [],
 [('config\\app_config.json', 'd:\\code\\OF\\config\\app_config.json', 'DATA'),
  ('config\\matching_config.json',
   'd:\\code\\OF\\config\\matching_config.json',
   'DATA'),
  ('src\\__init__.py', 'd:\\code\\OF\\src\\__init__.py', 'DATA'),
  ('src\\config\\__init__.py',
   'd:\\code\\OF\\src\\config\\__init__.py',
   'DATA'),
  ('src\\config\\config_manager.py',
   'd:\\code\\OF\\src\\config\\config_manager.py',
   'DATA'),
  ('src\\core\\__init__.py', 'd:\\code\\OF\\src\\core\\__init__.py', 'DATA'),
  ('src\\core\\app_launcher.py',
   'd:\\code\\OF\\src\\core\\app_launcher.py',
   'DATA'),
  ('src\\core\\fast_launcher.py',
   'd:\\code\\OF\\src\\core\\fast_launcher.py',
   'DATA'),
  ('src\\logo\\logo.ico', 'd:\\code\\OF\\src\\logo\\logo.ico', 'DATA'),
  ('src\\logo\\logo.png', 'd:\\code\\OF\\src\\logo\\logo.png', 'DATA'),
  ('src\\models\\__init__.py',
   'd:\\code\\OF\\src\\models\\__init__.py',
   'DATA'),
  ('src\\models\\surgery_record.py',
   'd:\\code\\OF\\src\\models\\surgery_record.py',
   'DATA'),
  ('src\\services\\__init__.py',
   'd:\\code\\OF\\src\\services\\__init__.py',
   'DATA'),
  ('src\\services\\data_import_service.py',
   'd:\\code\\OF\\src\\services\\data_import_service.py',
   'DATA'),
  ('src\\services\\export_service.py',
   'd:\\code\\OF\\src\\services\\export_service.py',
   'DATA'),
  ('src\\services\\matching_service.py',
   'd:\\code\\OF\\src\\services\\matching_service.py',
   'DATA'),
  ('src\\ui\\__init__.py', 'd:\\code\\OF\\src\\ui\\__init__.py', 'DATA'),
  ('src\\ui\\about_dialog.py',
   'd:\\code\\OF\\src\\ui\\about_dialog.py',
   'DATA'),
  ('src\\ui\\content_manager.py',
   'd:\\code\\OF\\src\\ui\\content_manager.py',
   'DATA'),
  ('src\\ui\\file_import_widget.py',
   'd:\\code\\OF\\src\\ui\\file_import_widget.py',
   'DATA'),
  ('src\\ui\\help_dialog.py', 'd:\\code\\OF\\src\\ui\\help_dialog.py', 'DATA'),
  ('src\\ui\\main_window.py', 'd:\\code\\OF\\src\\ui\\main_window.py', 'DATA'),
  ('src\\ui\\matching_config_widget.py',
   'd:\\code\\OF\\src\\ui\\matching_config_widget.py',
   'DATA'),
  ('src\\ui\\result_display_widget.py',
   'd:\\code\\OF\\src\\ui\\result_display_widget.py',
   'DATA'),
  ('src\\ui\\stat_card_widget.py',
   'd:\\code\\OF\\src\\ui\\stat_card_widget.py',
   'DATA'),
  ('src\\ui\\styles.py', 'd:\\code\\OF\\src\\ui\\styles.py', 'DATA'),
  ('src\\utils\\__init__.py', 'd:\\code\\OF\\src\\utils\\__init__.py', 'DATA'),
  ('src\\utils\\logger.py', 'd:\\code\\OF\\src\\utils\\logger.py', 'DATA')],
 '3.10.11 (tags/v3.10.11:7d4cc5a, Apr  5 2023, 00:38:17) [MSC v.1929 64 bit '
 '(AMD64)]',
 [('pyi_rth_inspect',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main', 'D:\\code\\OF\\main.py', 'PYSOURCE-2')],
 [('_pyi_rth_utils.qt',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('importlib',
   'D:\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('importlib.abc',
   'D:\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('typing', 'D:\\Python\\Python310\\lib\\typing.py', 'PYMODULE-2'),
  ('contextlib', 'D:\\Python\\Python310\\lib\\contextlib.py', 'PYMODULE-2'),
  ('importlib._abc',
   'D:\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'D:\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('email.message',
   'D:\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.policy',
   'D:\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'D:\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'D:\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('string', 'D:\\Python\\Python310\\lib\\string.py', 'PYMODULE-2'),
  ('email.headerregistry',
   'D:\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'D:\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('urllib', 'D:\\Python\\Python310\\lib\\urllib\\__init__.py', 'PYMODULE-2'),
  ('email.iterators',
   'D:\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.generator',
   'D:\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE-2'),
  ('copy', 'D:\\Python\\Python310\\lib\\copy.py', 'PYMODULE-2'),
  ('random', 'D:\\Python\\Python310\\lib\\random.py', 'PYMODULE-2'),
  ('statistics', 'D:\\Python\\Python310\\lib\\statistics.py', 'PYMODULE-2'),
  ('decimal', 'D:\\Python\\Python310\\lib\\decimal.py', 'PYMODULE-2'),
  ('_pydecimal', 'D:\\Python\\Python310\\lib\\_pydecimal.py', 'PYMODULE-2'),
  ('contextvars', 'D:\\Python\\Python310\\lib\\contextvars.py', 'PYMODULE-2'),
  ('fractions', 'D:\\Python\\Python310\\lib\\fractions.py', 'PYMODULE-2'),
  ('numbers', 'D:\\Python\\Python310\\lib\\numbers.py', 'PYMODULE-2'),
  ('hashlib', 'D:\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE-2'),
  ('logging', 'D:\\Python\\Python310\\lib\\logging\\__init__.py', 'PYMODULE-2'),
  ('pickle', 'D:\\Python\\Python310\\lib\\pickle.py', 'PYMODULE-2'),
  ('pprint', 'D:\\Python\\Python310\\lib\\pprint.py', 'PYMODULE-2'),
  ('dataclasses', 'D:\\Python\\Python310\\lib\\dataclasses.py', 'PYMODULE-2'),
  ('inspect', 'D:\\Python\\Python310\\lib\\inspect.py', 'PYMODULE-2'),
  ('argparse', 'D:\\Python\\Python310\\lib\\argparse.py', 'PYMODULE-2'),
  ('shutil', 'D:\\Python\\Python310\\lib\\shutil.py', 'PYMODULE-2'),
  ('tarfile', 'D:\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE-2'),
  ('gzip', 'D:\\Python\\Python310\\lib\\gzip.py', 'PYMODULE-2'),
  ('_compression', 'D:\\Python\\Python310\\lib\\_compression.py', 'PYMODULE-2'),
  ('lzma', 'D:\\Python\\Python310\\lib\\lzma.py', 'PYMODULE-2'),
  ('bz2', 'D:\\Python\\Python310\\lib\\bz2.py', 'PYMODULE-2'),
  ('fnmatch', 'D:\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE-2'),
  ('gettext', 'D:\\Python\\Python310\\lib\\gettext.py', 'PYMODULE-2'),
  ('token', 'D:\\Python\\Python310\\lib\\token.py', 'PYMODULE-2'),
  ('dis', 'D:\\Python\\Python310\\lib\\dis.py', 'PYMODULE-2'),
  ('opcode', 'D:\\Python\\Python310\\lib\\opcode.py', 'PYMODULE-2'),
  ('ast', 'D:\\Python\\Python310\\lib\\ast.py', 'PYMODULE-2'),
  ('_compat_pickle',
   'D:\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('struct', 'D:\\Python\\Python310\\lib\\struct.py', 'PYMODULE-2'),
  ('threading', 'D:\\Python\\Python310\\lib\\threading.py', 'PYMODULE-2'),
  ('_threading_local',
   'D:\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE-2'),
  ('bisect', 'D:\\Python\\Python310\\lib\\bisect.py', 'PYMODULE-2'),
  ('_strptime', 'D:\\Python\\Python310\\lib\\_strptime.py', 'PYMODULE-2'),
  ('datetime', 'D:\\Python\\Python310\\lib\\datetime.py', 'PYMODULE-2'),
  ('calendar', 'D:\\Python\\Python310\\lib\\calendar.py', 'PYMODULE-2'),
  ('email._encoded_words',
   'D:\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('base64', 'D:\\Python\\Python310\\lib\\base64.py', 'PYMODULE-2'),
  ('getopt', 'D:\\Python\\Python310\\lib\\getopt.py', 'PYMODULE-2'),
  ('email.charset',
   'D:\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.encoders',
   'D:\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'D:\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email._policybase',
   'D:\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.header',
   'D:\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.errors',
   'D:\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.utils', 'D:\\Python\\Python310\\lib\\email\\utils.py', 'PYMODULE-2'),
  ('email._parseaddr',
   'D:\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('urllib.parse',
   'D:\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('socket', 'D:\\Python\\Python310\\lib\\socket.py', 'PYMODULE-2'),
  ('selectors', 'D:\\Python\\Python310\\lib\\selectors.py', 'PYMODULE-2'),
  ('quopri', 'D:\\Python\\Python310\\lib\\quopri.py', 'PYMODULE-2'),
  ('uu', 'D:\\Python\\Python310\\lib\\uu.py', 'PYMODULE-2'),
  ('optparse', 'D:\\Python\\Python310\\lib\\optparse.py', 'PYMODULE-2'),
  ('textwrap', 'D:\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE-2'),
  ('zipfile', 'D:\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE-2'),
  ('py_compile', 'D:\\Python\\Python310\\lib\\py_compile.py', 'PYMODULE-2'),
  ('importlib.util',
   'D:\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('email', 'D:\\Python\\Python310\\lib\\email\\__init__.py', 'PYMODULE-2'),
  ('email.parser',
   'D:\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'D:\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('csv', 'D:\\Python\\Python310\\lib\\csv.py', 'PYMODULE-2'),
  ('importlib.readers',
   'D:\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('tokenize', 'D:\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE-2'),
  ('importlib._bootstrap',
   'D:\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('subprocess', 'D:\\Python\\Python310\\lib\\subprocess.py', 'PYMODULE-2'),
  ('signal', 'D:\\Python\\Python310\\lib\\signal.py', 'PYMODULE-2'),
  ('multiprocessing.spawn',
   'D:\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'D:\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'D:\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'D:\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'D:\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'D:\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('xmlrpc.client',
   'D:\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE-2'),
  ('xmlrpc', 'D:\\Python\\Python310\\lib\\xmlrpc\\__init__.py', 'PYMODULE-2'),
  ('xml.parsers.expat',
   'D:\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'D:\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml', 'D:\\Python\\Python310\\lib\\xml\\__init__.py', 'PYMODULE-2'),
  ('xml.sax.expatreader',
   'D:\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'D:\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('urllib.request',
   'D:\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('getpass', 'D:\\Python\\Python310\\lib\\getpass.py', 'PYMODULE-2'),
  ('nturl2path', 'D:\\Python\\Python310\\lib\\nturl2path.py', 'PYMODULE-2'),
  ('ftplib', 'D:\\Python\\Python310\\lib\\ftplib.py', 'PYMODULE-2'),
  ('netrc', 'D:\\Python\\Python310\\lib\\netrc.py', 'PYMODULE-2'),
  ('shlex', 'D:\\Python\\Python310\\lib\\shlex.py', 'PYMODULE-2'),
  ('mimetypes', 'D:\\Python\\Python310\\lib\\mimetypes.py', 'PYMODULE-2'),
  ('http.cookiejar',
   'D:\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('http', 'D:\\Python\\Python310\\lib\\http\\__init__.py', 'PYMODULE-2'),
  ('ssl', 'D:\\Python\\Python310\\lib\\ssl.py', 'PYMODULE-2'),
  ('urllib.response',
   'D:\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('urllib.error',
   'D:\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE-2'),
  ('xml.sax',
   'D:\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE-2'),
  ('xml.sax.handler',
   'D:\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE-2'),
  ('xml.sax._exceptions',
   'D:\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'D:\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('http.client', 'D:\\Python\\Python310\\lib\\http\\client.py', 'PYMODULE-2'),
  ('hmac', 'D:\\Python\\Python310\\lib\\hmac.py', 'PYMODULE-2'),
  ('tempfile', 'D:\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE-2'),
  ('multiprocessing.context',
   'D:\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'D:\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'D:\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('ctypes', 'D:\\Python\\Python310\\lib\\ctypes\\__init__.py', 'PYMODULE-2'),
  ('ctypes._endian',
   'D:\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'D:\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'D:\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'D:\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('queue', 'D:\\Python\\Python310\\lib\\queue.py', 'PYMODULE-2'),
  ('multiprocessing.queues',
   'D:\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'D:\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'D:\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'D:\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('secrets', 'D:\\Python\\Python310\\lib\\secrets.py', 'PYMODULE-2'),
  ('multiprocessing.reduction',
   'D:\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'D:\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('runpy', 'D:\\Python\\Python310\\lib\\runpy.py', 'PYMODULE-2'),
  ('pkgutil', 'D:\\Python\\Python310\\lib\\pkgutil.py', 'PYMODULE-2'),
  ('zipimport', 'D:\\Python\\Python310\\lib\\zipimport.py', 'PYMODULE-2'),
  ('multiprocessing',
   'D:\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('fuzzywuzzy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\__init__.py',
   'PYMODULE-2'),
  ('fuzzywuzzy.fuzz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\fuzz.py',
   'PYMODULE-2'),
  ('difflib', 'D:\\Python\\Python310\\lib\\difflib.py', 'PYMODULE-2'),
  ('fuzzywuzzy.StringMatcher',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\StringMatcher.py',
   'PYMODULE-2'),
  ('Levenshtein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\Levenshtein\\__init__.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\__init__.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance._initialize',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\_initialize.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance._initialize_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\_initialize_py.py',
   'PYMODULE-2'),
  ('rapidfuzz._feature_detector',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\_feature_detector.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Prefix',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Prefix.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.metrics_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\metrics_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Prefix_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Prefix_py.py',
   'PYMODULE-2'),
  ('rapidfuzz._common_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\_common_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Postfix_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Postfix_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.OSA_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\OSA_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Levenshtein_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Levenshtein_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.LCSseq_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\LCSseq_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.JaroWinkler_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\JaroWinkler_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Jaro_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Jaro_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Indel_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Indel_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Hamming_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Hamming_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.DamerauLevenshtein_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\DamerauLevenshtein_py.py',
   'PYMODULE-2'),
  ('rapidfuzz._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\_utils.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Postfix',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Postfix.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.LCSseq',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\LCSseq.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.DamerauLevenshtein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\DamerauLevenshtein.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.OSA',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\OSA.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Levenshtein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Levenshtein.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.JaroWinkler',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\JaroWinkler.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Jaro',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Jaro.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Indel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Indel.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Hamming',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Hamming.py',
   'PYMODULE-2'),
  ('platform', 'D:\\Python\\Python310\\lib\\platform.py', 'PYMODULE-2'),
  ('__future__', 'D:\\Python\\Python310\\lib\\__future__.py', 'PYMODULE-2'),
  ('fuzzywuzzy.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\utils.py',
   'PYMODULE-2'),
  ('fuzzywuzzy.string_processing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\string_processing.py',
   'PYMODULE-2'),
  ('rapidfuzz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\__init__.py',
   'PYMODULE-2'),
  ('rapidfuzz.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\utils.py',
   'PYMODULE-2'),
  ('rapidfuzz.utils_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\utils_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.process',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\process.py',
   'PYMODULE-2'),
  ('rapidfuzz.process_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\process_py.py',
   'PYMODULE-2'),
  ('numpy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.char',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('numpy.f2py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py.diagnose',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-2'),
  ('numpy.f2py.f2py2e',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._distutils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._backend',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._meson',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-2'),
  ('numpy.f2py.auxfuncs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.f90mod_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.use_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.common_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.func2subr',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-2'),
  ('numpy.f2py._isocbind',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-2'),
  ('numpy.f2py.crackfortran',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-2'),
  ('fileinput', 'D:\\Python\\Python310\\lib\\fileinput.py', 'PYMODULE-2'),
  ('numpy.f2py.symbolic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-2'),
  ('numpy.f2py.cb_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.capi_maps',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-2'),
  ('numpy.f2py.cfuncs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.__version__',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE-2'),
  ('sysconfig', 'D:\\Python\\Python310\\lib\\sysconfig.py', 'PYMODULE-2'),
  ('_aix_support', 'D:\\Python\\Python310\\lib\\_aix_support.py', 'PYMODULE-2'),
  ('_bootsubprocess',
   'D:\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE-2'),
  ('numpy._core._internal',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('pydoc', 'D:\\Python\\Python310\\lib\\pydoc.py', 'PYMODULE-2'),
  ('webbrowser', 'D:\\Python\\Python310\\lib\\webbrowser.py', 'PYMODULE-2'),
  ('glob', 'D:\\Python\\Python310\\lib\\glob.py', 'PYMODULE-2'),
  ('http.server', 'D:\\Python\\Python310\\lib\\http\\server.py', 'PYMODULE-2'),
  ('socketserver', 'D:\\Python\\Python310\\lib\\socketserver.py', 'PYMODULE-2'),
  ('html', 'D:\\Python\\Python310\\lib\\html\\__init__.py', 'PYMODULE-2'),
  ('html.entities',
   'D:\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE-2'),
  ('pydoc_data.topics',
   'D:\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('pydoc_data',
   'D:\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE-2'),
  ('tty', 'D:\\Python\\Python310\\lib\\tty.py', 'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('json', 'D:\\Python\\Python310\\lib\\json\\__init__.py', 'PYMODULE-2'),
  ('json.encoder',
   'D:\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE-2'),
  ('json.decoder',
   'D:\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE-2'),
  ('json.scanner',
   'D:\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy.version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('rapidfuzz.process_cpp',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\process_cpp.py',
   'PYMODULE-2'),
  ('rapidfuzz.fuzz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\fuzz.py',
   'PYMODULE-2'),
  ('rapidfuzz.fuzz_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\fuzz_py.py',
   'PYMODULE-2'),
  ('openpyxl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl._constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE-2'),
  ('openpyxl.reader.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.reader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.reader.drawings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE-2'),
  ('openpyxl.chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reference',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE-2'),
  ('openpyxl.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.utils.formulas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE-2'),
  ('openpyxl.formula',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formula.tokenizer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE-2'),
  ('openpyxl.utils.cell',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.worksheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.print_settings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.formula',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.scenario',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.styles.colors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.styles',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.styles.named_styles',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE-2'),
  ('openpyxl.styles.cell_style',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE-2'),
  ('openpyxl.utils.indexed_list',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.styles.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.styles.numbers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fonts',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.nested',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.namespace',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE-2'),
  ('openpyxl.utils.datetime',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fills',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE-2'),
  ('openpyxl.styles.borders',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE-2'),
  ('openpyxl.styles.alignment',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.sequence',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.merge',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE-2'),
  ('openpyxl.cell.cell',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.cell.rich_text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE-2'),
  ('openpyxl.cell.text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE-2'),
  ('openpyxl.styles.styleable',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE-2'),
  ('openpyxl.styles.builtins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE-2'),
  ('openpyxl.styles.proxy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.cell_range',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.views',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.filters',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.utils.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.dimensions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE-2'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE-2'),
  ('openpyxl.utils.units',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.page',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE-2'),
  ('openpyxl.formula.translate',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.defined_name',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.child',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.header_footer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE-2'),
  ('openpyxl.utils.escape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.formatting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE-2'),
  ('openpyxl.formatting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.rule',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE-2'),
  ('openpyxl.styles.differential',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE-2'),
  ('openpyxl.compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.compat.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.serialisable',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series_factory',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE-2'),
  ('openpyxl.chart.trendline',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE-2'),
  ('openpyxl.chart.layout',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE-2'),
  ('openpyxl.chart.text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE-2'),
  ('openpyxl.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.geometry',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.line',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.fill',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.effect',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.colors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.chart.marker',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE-2'),
  ('openpyxl.chart.picture',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.chart.label',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE-2'),
  ('openpyxl.chart.error_bar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE-2'),
  ('openpyxl.chart.shapes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE-2'),
  ('openpyxl.chart.data_source',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE-2'),
  ('openpyxl.chart.surface_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.axis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE-2'),
  ('openpyxl.chart.title',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE-2'),
  ('openpyxl.chart.descriptors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE-2'),
  ('openpyxl.chart._3d',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE-2'),
  ('openpyxl.chart._chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.legend',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE-2'),
  ('openpyxl.chart.stock_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.updown_bars',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE-2'),
  ('openpyxl.chart.scatter_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.radar_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pie_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.line_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bubble_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bar_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.area_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.chartspace',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE-2'),
  ('openpyxl.chart.print_settings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pivot',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE-2'),
  ('openpyxl.chart.plotarea',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.image',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE-2'),
  ('PIL.Image',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-2'),
  ('PIL.XpmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XbmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XVThumbImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WmfImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WebPImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TgaImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SunImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SpiderImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageTk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-2'),
  ('PIL.SgiImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.QoiImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PsdImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PixarImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.features',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE-2'),
  ('PIL.PdfParser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-2'),
  ('PIL.ImageSequence',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-2'),
  ('PIL.PcxImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcdImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PalmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MspImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpoImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpegImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MicImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.McIdasImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IptcImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImtImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcoImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcnsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GribStubImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GbrImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FtexImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FpxImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FliImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FitsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.EpsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DdsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DcxImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.CurImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BufrStubImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BlpImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.AvifImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageShow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-2'),
  ('PIL.ImageColor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-2'),
  ('colorsys', 'D:\\Python\\Python310\\lib\\colorsys.py', 'PYMODULE-2'),
  ('PIL.ImageCms',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-2'),
  ('PIL.ImageWin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-2'),
  ('PIL.PngImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageChops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-2'),
  ('PIL.PpmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegPresets',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-2'),
  ('PIL.GifImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageOps',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-2'),
  ('PIL.ImageMath',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-2'),
  ('PIL.BmpImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL._typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-2'),
  ('PIL.TiffImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImageQt',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-2'),
  ('PIL.ImagePalette',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-2'),
  ('PIL.PaletteFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GimpPaletteFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GimpGradientFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-2'),
  ('PIL.ImageFilter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-2'),
  ('PIL.ImageFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'D:\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'D:\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'D:\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'D:\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree',
   'D:\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE-2'),
  ('PIL._util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-2'),
  ('PIL._deprecate',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-2'),
  ('PIL._binary',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-2'),
  ('PIL.TiffTags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-2'),
  ('PIL.ImageMode',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-2'),
  ('PIL.ExifTags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-2'),
  ('PIL',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-2'),
  ('PIL._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-2'),
  ('openpyxl.xml.functions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE-2'),
  ('et_xmlfile.xmlfile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE-2'),
  ('et_xmlfile.incremental_tree',
   'D:\\code\\OF\\venv\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE-2'),
  ('et_xmlfile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.relation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.picture',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.graphic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.connector',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.xdr',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.related',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.publish',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.custom',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.views',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.relation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._reader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._read_only',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE-2'),
  ('openpyxl.cell.read_only',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.relationship',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE-2'),
  ('openpyxl.packaging',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.container',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.manifest',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.custom',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE-2'),
  ('openpyxl.styles.stylesheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE-2'),
  ('openpyxl.styles.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE-2'),
  ('openpyxl.reader.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.record',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE-2'),
  ('openpyxl.pivot',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.fields',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.cache',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link.external',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.web',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.views',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.smart_tags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.function_group',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_reference',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE-2'),
  ('openpyxl.reader.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comment_sheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE-2'),
  ('openpyxl.comments',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.comments.shape_writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comments',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE-2'),
  ('openpyxl.comments.author',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE-2'),
  ('openpyxl.cell',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.xml.constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE-2'),
  ('openpyxl.utils.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE-2'),
  ('openpyxl.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.writer.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.extended',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE-2'),
  ('openpyxl.writer.theme',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE-2'),
  ('openpyxl.workbook._writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.cell._writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.copier',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._write_only',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE-2'),
  ('openpyxl.xml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.compat.numbers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE-2'),
  ('pandas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-2'),
  ('pandas._typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-2'),
  ('pandas.tseries.holiday',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE-2'),
  ('dateutil.relativedelta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-2'),
  ('dateutil',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.rrule',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-2'),
  ('dateutil.parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.parser.isoparser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-2'),
  ('dateutil.parser._parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-2'),
  ('dateutil.tz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.tz.tz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-2'),
  ('dateutil.zoneinfo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.tz.win',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-2'),
  ('ctypes.wintypes',
   'D:\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE-2'),
  ('dateutil.tz._factories',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-2'),
  ('dateutil.tz._common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-2'),
  ('dateutil.easter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-2'),
  ('dateutil._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-2'),
  ('dateutil._common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-2'),
  ('six', 'D:\\code\\OF\\venv\\lib\\site-packages\\six.py', 'PYMODULE-2'),
  ('pandas.io.formats.format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-2'),
  ('pandas.io.formats.csvs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.generic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arraylike',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-2'),
  ('pandas.core.ops.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-2'),
  ('pandas.core.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.ops.mask_ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.invalid',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-2'),
  ('pandas.core.ops.docstrings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-2'),
  ('pandas.core.ops.array_ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.dispatch',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expressions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-2'),
  ('pandas.core.computation.check',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-2'),
  ('pandas.compat._optional',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-2'),
  ('pandas.util.version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-2'),
  ('pandas.util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.util.hashing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-2'),
  ('pandas.core.util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.computation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expr',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-2'),
  ('pandas.core.computation.scope',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-2'),
  ('pandas.core.computation.parsing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-2'),
  ('pandas.core.computation.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.computation.eval',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-2'),
  ('pandas.core.computation.engines',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-2'),
  ('pandas.core.computation.align',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-2'),
  ('pandas.util._validators',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-2'),
  ('pandas.core.computation.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.cast',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.timedeltas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._ranges',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimelike',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.sorting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.grouper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.categorical',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.categorical',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.concat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.astype',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-2'),
  ('pandas.core.strings.object_array',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-2'),
  ('pandas.core.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.strings.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-2'),
  ('pandas.core.accessor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.groupby',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.groupby',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.var_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.shared',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-2'),
  ('pandas.core.window',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.window.expanding',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-2'),
  ('pandas.core.window.doc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-2'),
  ('pandas.core.shared_docs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.objects',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-2'),
  ('pandas.core.window.ewm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-2'),
  ('pandas.core.window.online',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-2'),
  ('pandas.core.window.numba_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-2'),
  ('pandas.core.util.numba_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.internals.blocks',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.transforms',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.replace',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.quantile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.putmask',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.indexing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_arrow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.masked',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.tools.numeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.tools',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.boolean',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.apply',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-2'),
  ('pandas.core._numba.extensions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE-2'),
  ('pandas.core._numba.executor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-2'),
  ('pandas.core._numba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.numba_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.period',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-2'),
  ('pickletools', 'D:\\Python\\Python310\\lib\\pickletools.py', 'PYMODULE-2'),
  ('doctest', 'D:\\Python\\Python310\\lib\\doctest.py', 'PYMODULE-2'),
  ('unittest',
   'D:\\Python\\Python310\\lib\\unittest\\__init__.py',
   'PYMODULE-2'),
  ('unittest.async_case',
   'D:\\Python\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE-2'),
  ('asyncio', 'D:\\Python\\Python310\\lib\\asyncio\\__init__.py', 'PYMODULE-2'),
  ('asyncio.unix_events',
   'D:\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.log', 'D:\\Python\\Python310\\lib\\asyncio\\log.py', 'PYMODULE-2'),
  ('asyncio.windows_events',
   'D:\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'D:\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'D:\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'D:\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'D:\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.threads',
   'D:\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'D:\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'D:\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'D:\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'D:\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.base_events',
   'D:\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent',
   'D:\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'D:\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'D:\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.tasks',
   'D:\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE-2'),
  ('asyncio.locks',
   'D:\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'D:\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.mixins',
   'D:\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'D:\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'D:\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'D:\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'D:\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'D:\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'D:\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'D:\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'D:\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'D:\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'D:\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('unittest.signals',
   'D:\\Python\\Python310\\lib\\unittest\\signals.py',
   'PYMODULE-2'),
  ('unittest.main',
   'D:\\Python\\Python310\\lib\\unittest\\main.py',
   'PYMODULE-2'),
  ('unittest.runner',
   'D:\\Python\\Python310\\lib\\unittest\\runner.py',
   'PYMODULE-2'),
  ('unittest.loader',
   'D:\\Python\\Python310\\lib\\unittest\\loader.py',
   'PYMODULE-2'),
  ('unittest.suite',
   'D:\\Python\\Python310\\lib\\unittest\\suite.py',
   'PYMODULE-2'),
  ('unittest.case',
   'D:\\Python\\Python310\\lib\\unittest\\case.py',
   'PYMODULE-2'),
  ('unittest._log',
   'D:\\Python\\Python310\\lib\\unittest\\_log.py',
   'PYMODULE-2'),
  ('unittest.result',
   'D:\\Python\\Python310\\lib\\unittest\\result.py',
   'PYMODULE-2'),
  ('unittest.util',
   'D:\\Python\\Python310\\lib\\unittest\\util.py',
   'PYMODULE-2'),
  ('pdb', 'D:\\Python\\Python310\\lib\\pdb.py', 'PYMODULE-2'),
  ('code', 'D:\\Python\\Python310\\lib\\code.py', 'PYMODULE-2'),
  ('codeop', 'D:\\Python\\Python310\\lib\\codeop.py', 'PYMODULE-2'),
  ('bdb', 'D:\\Python\\Python310\\lib\\bdb.py', 'PYMODULE-2'),
  ('cmd', 'D:\\Python\\Python310\\lib\\cmd.py', 'PYMODULE-2'),
  ('pandas.core.arrays.interval',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-2'),
  ('pandas.tseries.frequencies',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-2'),
  ('pandas.core.indexers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._mixins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy.function',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.array',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-2'),
  ('pandas.core.tools.times',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-2'),
  ('pandas.core.tools.timedeltas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.tools.datetimes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.floating',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.integer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-2'),
  ('pandas.io._util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.inference',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-2'),
  ('pandas.util._exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-2'),
  ('pandas.core.ops.missing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.construction',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.algorithms',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-2'),
  ('pandas.core.internals.construction',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.internals.managers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-2'),
  ('pandas.api.extensions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.internals.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.internals.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-2'),
  ('pandas.core.internals.array_manager',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.tile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-2'),
  ('pandas.core.reshape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.take',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-2'),
  ('pandas.core.sample',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-2'),
  ('pandas.core.indexing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.nanops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-2'),
  ('pandas.core.missing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.roperator',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-2'),
  ('pandas.util._decorators',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-2'),
  ('pandas.io.formats.string',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-2'),
  ('pandas.io.formats.html',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.multi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-2'),
  ('pandas.core.indexes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.frozen',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-2'),
  ('pandas.io.formats.console',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-2'),
  ('pandas.io.formats.printing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-2'),
  ('pandas.io.formats',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-2'),
  ('pandas.io.formats.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-2'),
  ('pandas.io.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-2'),
  ('xlsxwriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE-2'),
  ('xlsxwriter.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE-2'),
  ('xlsxwriter.utility',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE-2'),
  ('xlsxwriter.color',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE-2'),
  ('xlsxwriter.sharedstrings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE-2'),
  ('xlsxwriter.packager',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE-2'),
  ('xlsxwriter.vml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE-2'),
  ('xlsxwriter.theme',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE-2'),
  ('xlsxwriter.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE-2'),
  ('xlsxwriter.styles',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_types',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_structure',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_rel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE-2'),
  ('xlsxwriter.relationships',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE-2'),
  ('xlsxwriter.metadata',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE-2'),
  ('xlsxwriter.feature_property_bag',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE-2'),
  ('xlsxwriter.custom',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE-2'),
  ('xlsxwriter.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE-2'),
  ('xlsxwriter.contenttypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE-2'),
  ('xlsxwriter.comments',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE-2'),
  ('xlsxwriter.app',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE-2'),
  ('xlsxwriter.format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE-2'),
  ('xlsxwriter.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE-2'),
  ('xlsxwriter.chartsheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE-2'),
  ('xlsxwriter.shape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE-2'),
  ('xlsxwriter.url',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_stock',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_scatter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_radar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_line',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_doughnut',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_column',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_bar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_area',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE-2'),
  ('xlsxwriter.image',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE-2'),
  ('xlsxwriter.worksheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_pie',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE-2'),
  ('xlsxwriter.xmlwriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE-2'),
  ('pandas.io.excel._util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.excel._openpyxl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odswriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-2'),
  ('pandas.io.excel._base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-2'),
  ('xlrd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\__init__.py',
   'PYMODULE-2'),
  ('xlrd.xldate',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE-2'),
  ('xlrd.info',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\info.py',
   'PYMODULE-2'),
  ('xlrd.formula',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE-2'),
  ('xlrd.book',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\book.py',
   'PYMODULE-2'),
  ('xlrd.sheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\sheet.py',
   'PYMODULE-2'),
  ('xlrd.formatting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE-2'),
  ('xlrd.compdoc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE-2'),
  ('xlrd.biffh',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\biffh.py',
   'PYMODULE-2'),
  ('xlrd.timemachine',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlrd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-2'),
  ('pandas.io.excel._pyxlsb',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odfreader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-2'),
  ('pandas.io.excel._calamine',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.readers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.python_parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.base_parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.formats.css',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-2'),
  ('pandas.io.formats._color_data',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style_render',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-2'),
  ('pandas.api.types',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-2'),
  ('uuid', 'D:\\Python\\Python310\\lib\\uuid.py', 'PYMODULE-2'),
  ('pandas.io.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.concat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.timedeltas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.extension',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimelike',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.range',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-2'),
  ('pytz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-2'),
  ('pytz.tzfile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-2'),
  ('pytz.tzinfo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-2'),
  ('pytz.lazy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-2'),
  ('pytz.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.period',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.interval',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.category',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-2'),
  ('pandas.core.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-2'),
  ('pandas.core.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numpy_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.array',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.missing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-2'),
  ('pandas._config.config',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-2'),
  ('pandas.core.window.rolling',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-2'),
  ('pandas.core.series',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.reshape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-2'),
  ('pandas.io.formats.info',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-2'),
  ('pandas.core.strings.accessor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.methods.selectn',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-2'),
  ('pandas.core.methods',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.accessors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-2'),
  ('pandas.compat._constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-2'),
  ('pandas.core.resample',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-2'),
  ('pandas.core.internals',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.internals.concat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.internals.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.merge',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.generic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.generic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-2'),
  ('pandas.io.clipboards',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-2'),
  ('pandas.io.clipboard',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.pickle',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-2'),
  ('pandas.compat.pickle_compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-2'),
  ('pandas.io.sql',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-2'),
  ('sqlite3', 'D:\\Python\\Python310\\lib\\sqlite3\\__init__.py', 'PYMODULE-2'),
  ('sqlite3.dump',
   'D:\\Python\\Python310\\lib\\sqlite3\\dump.py',
   'PYMODULE-2'),
  ('sqlite3.dbapi2',
   'D:\\Python\\Python310\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE-2'),
  ('pandas.io.pytables',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-2'),
  ('pandas.core.computation.pytables',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-2'),
  ('pandas.io.json',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.json._table_schema',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-2'),
  ('pandas.io.json._json',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-2'),
  ('pandas.core.methods.describe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-2'),
  ('pandas.core.flags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-2'),
  ('pandas.core.frame',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.pivot',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-2'),
  ('pandas.io.formats.xml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-2'),
  ('xml.dom.minidom',
   'D:\\Python\\Python310\\lib\\xml\\dom\\minidom.py',
   'PYMODULE-2'),
  ('xml.dom.pulldom',
   'D:\\Python\\Python310\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE-2'),
  ('xml.dom.expatbuilder',
   'D:\\Python\\Python310\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-2'),
  ('xml.dom.NodeFilter',
   'D:\\Python\\Python310\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-2'),
  ('xml.dom.xmlbuilder',
   'D:\\Python\\Python310\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-2'),
  ('xml.dom.minicompat',
   'D:\\Python\\Python310\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE-2'),
  ('xml.dom.domreg',
   'D:\\Python\\Python310\\lib\\xml\\dom\\domreg.py',
   'PYMODULE-2'),
  ('xml.dom',
   'D:\\Python\\Python310\\lib\\xml\\dom\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.xml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-2'),
  ('pandas.io.orc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-2'),
  ('pandas.io.parquet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-2'),
  ('pandas.io.feather_format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-2'),
  ('pandas.io.stata',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-2'),
  ('pandas.io.gbq',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-2'),
  ('pandas.core.methods.to_dict',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.column',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.buffer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.melt',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-2'),
  ('pandas._libs.window',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.tslibs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas7bdat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sasreader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_xport',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-2'),
  ('pandas.io.sas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-2'),
  ('pandas._version_meson',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE-2'),
  ('pandas.util._tester',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-2'),
  ('pandas.io.json._normalize',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-2'),
  ('pandas.io.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-2'),
  ('pandas.io.spss',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-2'),
  ('pandas.io.html',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-2'),
  ('pandas.util._print_versions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-2'),
  ('pandas.testing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-2'),
  ('pandas._testing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing.contexts',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-2'),
  ('pandas._testing.compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-2'),
  ('pandas._testing.asserters',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-2'),
  ('pandas._testing._warnings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-2'),
  ('pandas._testing._io',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-2'),
  ('pandas._config.localization',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-2'),
  ('pandas.plotting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-2'),
  ('pandas.plotting._misc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-2'),
  ('pandas.plotting._core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-2'),
  ('pandas.io',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-2'),
  ('pandas.errors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-2'),
  ('pandas.arrays',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.interchange',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-2'),
  ('pandas.api.indexers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.encoding',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-2'),
  ('pandas.core.computation.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-2'),
  ('pandas.tseries.offsets',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-2'),
  ('pandas.tseries',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-2'),
  ('pandas.tseries.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.dtypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-2'),
  ('pandas.core.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-2'),
  ('pandas.core.config_init',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-2'),
  ('pandas._config',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config.display',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-2'),
  ('pandas._config.dates',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-2'),
  ('pandas.compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat.pyarrow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-2'),
  ('pandas.compat.compressors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-2'),
  ('shiboken6',
   'D:\\code\\OF\\venv\\lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE-2'),
  ('PySide6',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE-2'),
  ('PySide6.support.deprecated',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE-2'),
  ('PySide6.support',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE-2'),
  ('stringprep', 'D:\\Python\\Python310\\lib\\stringprep.py', 'PYMODULE-2'),
  ('tracemalloc', 'D:\\Python\\Python310\\lib\\tracemalloc.py', 'PYMODULE-2'),
  ('_py_abc', 'D:\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE-2'),
  ('src.core.fast_launcher',
   'd:\\code\\OF\\src\\core\\fast_launcher.py',
   'PYMODULE-2'),
  ('src.core', 'd:\\code\\OF\\src\\core\\__init__.py', 'PYMODULE-2'),
  ('src', 'd:\\code\\OF\\src\\__init__.py', 'PYMODULE-2'),
  ('src.ui.file_import_widget',
   'd:\\code\\OF\\src\\ui\\file_import_widget.py',
   'PYMODULE-2'),
  ('src.ui', 'd:\\code\\OF\\src\\ui\\__init__.py', 'PYMODULE-2'),
  ('src.ui.stat_card_widget',
   'd:\\code\\OF\\src\\ui\\stat_card_widget.py',
   'PYMODULE-2'),
  ('src.ui.result_display_widget',
   'd:\\code\\OF\\src\\ui\\result_display_widget.py',
   'PYMODULE-2'),
  ('src.models.surgery_record',
   'd:\\code\\OF\\src\\models\\surgery_record.py',
   'PYMODULE-2'),
  ('src.models', 'd:\\code\\OF\\src\\models\\__init__.py', 'PYMODULE-2'),
  ('src.ui.matching_config_widget',
   'd:\\code\\OF\\src\\ui\\matching_config_widget.py',
   'PYMODULE-2'),
  ('src.ui.main_window', 'd:\\code\\OF\\src\\ui\\main_window.py', 'PYMODULE-2'),
  ('src.services.export_service',
   'd:\\code\\OF\\src\\services\\export_service.py',
   'PYMODULE-2'),
  ('src.services', 'd:\\code\\OF\\src\\services\\__init__.py', 'PYMODULE-2'),
  ('src.ui.about_dialog',
   'd:\\code\\OF\\src\\ui\\about_dialog.py',
   'PYMODULE-2'),
  ('src.ui.content_manager',
   'd:\\code\\OF\\src\\ui\\content_manager.py',
   'PYMODULE-2'),
  ('src.ui.help_dialog', 'd:\\code\\OF\\src\\ui\\help_dialog.py', 'PYMODULE-2'),
  ('src.ui.styles', 'd:\\code\\OF\\src\\ui\\styles.py', 'PYMODULE-2'),
  ('src.utils.logger', 'd:\\code\\OF\\src\\utils\\logger.py', 'PYMODULE-2'),
  ('src.utils', 'd:\\code\\OF\\src\\utils\\__init__.py', 'PYMODULE-2'),
  ('logging.handlers',
   'D:\\Python\\Python310\\lib\\logging\\handlers.py',
   'PYMODULE-2'),
  ('smtplib', 'D:\\Python\\Python310\\lib\\smtplib.py', 'PYMODULE-2'),
  ('src.services.matching_service',
   'd:\\code\\OF\\src\\services\\matching_service.py',
   'PYMODULE-2'),
  ('src.services.data_import_service',
   'd:\\code\\OF\\src\\services\\data_import_service.py',
   'PYMODULE-2'),
  ('src.core.app_launcher',
   'd:\\code\\OF\\src\\core\\app_launcher.py',
   'PYMODULE-2'),
  ('src.config.config_manager',
   'd:\\code\\OF\\src\\config\\config_manager.py',
   'PYMODULE-2'),
  ('src.config', 'd:\\code\\OF\\src\\config\\__init__.py', 'PYMODULE-2'),
  ('pathlib', 'D:\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE-2')],
 [('python310.dll', 'D:\\Python\\Python310\\python310.dll', 'BINARY'),
  ('numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy.libs\\libscipy_openblas64_-13e2df515630b4a41f92893938845698.dll',
   'BINARY'),
  ('numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy.libs\\msvcp140-263139962577ecda4cd9469ca360a746.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\styles\\qmodernwindowsstyle.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('_decimal.pyd', 'D:\\Python\\Python310\\DLLs\\_decimal.pyd', 'EXTENSION'),
  ('_lzma.pyd', 'D:\\Python\\Python310\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\Python\\Python310\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_hashlib.pyd', 'D:\\Python\\Python310\\DLLs\\_hashlib.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Python\\Python310\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd', 'D:\\Python\\Python310\\DLLs\\select.pyd', 'EXTENSION'),
  ('_socket.pyd', 'D:\\Python\\Python310\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Python\\Python310\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd', 'D:\\Python\\Python310\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\Python\\Python310\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\Python\\Python310\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\Python\\Python310\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('Levenshtein\\levenshtein_cpp.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\Levenshtein\\levenshtein_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\distance\\_initialize_cpp.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\_initialize_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\_feature_detector_cpp.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\_feature_detector_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\distance\\metrics_cpp.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\metrics_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\distance\\metrics_cpp_avx2.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\metrics_cpp_avx2.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\utils_cpp.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\utils_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_multiarray_tests.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_multiarray_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\mtrand.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_sfc64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_philox.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_pcg64.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_mt19937.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\bit_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_generator.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_bounded_integers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_common.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft_umath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\process_cpp_impl.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\process_cpp_impl.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\fuzz_cpp.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\fuzz_cpp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('rapidfuzz\\fuzz_cpp_avx2.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\fuzz_cpp_avx2.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_webp.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_imagingtk.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_avif.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_avif.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_imagingcms.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_imagingmath.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Python\\Python310\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_imaging.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\writers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Python\\Python310\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd', 'D:\\Python\\Python310\\DLLs\\_asyncio.pyd', 'EXTENSION'),
  ('_uuid.pyd', 'D:\\Python\\Python310\\DLLs\\_uuid.pyd', 'EXTENSION'),
  ('_sqlite3.pyd', 'D:\\Python\\Python310\\DLLs\\_sqlite3.pyd', 'EXTENSION'),
  ('pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\window\\indexers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\window\\aggregations.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\vectorized.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\tzconversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\timezones.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\timestamps.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\timedeltas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\strptime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\period.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\parsing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\offsets.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\np_datetime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\nattype.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\fields.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\dtypes.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\conversion.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\ccalendar.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslibs\\base.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\base.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\testing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\sparse.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\sas.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\sas.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\reshape.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\properties.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\parsers.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_parser.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\pandas_parser.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\pandas_datetime.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\pandas_datetime.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\ops_dispatch.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\ops.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\missing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\lib.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\json.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\json.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\join.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\join.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\interval.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\internals.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\indexing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\index.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\index.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\hashtable.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\hashing.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\groupby.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\byteswap.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\byteswap.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\arrays.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\algos.cp310-win_amd64.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140.dll', 'D:\\Python\\Python310\\VCRUNTIME140.dll', 'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll', 'D:\\Python\\Python310\\VCRUNTIME140_1.dll', 'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('PySide6\\Qt6Gui.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('shiboken6\\MSVCP140.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\shiboken6\\MSVCP140.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('libcrypto-1_1.dll',
   'D:\\Python\\Python310\\DLLs\\libcrypto-1_1.dll',
   'BINARY'),
  ('libssl-1_1.dll', 'D:\\Python\\Python310\\DLLs\\libssl-1_1.dll', 'BINARY'),
  ('libffi-7.dll', 'D:\\Python\\Python310\\DLLs\\libffi-7.dll', 'BINARY'),
  ('sqlite3.dll', 'D:\\Python\\Python310\\DLLs\\sqlite3.dll', 'BINARY'),
  ('pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas.libs\\msvcp140-1a0962f2a91a74c6d7136a768987a591.dll',
   'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Python\\Python310\\python3.dll', 'BINARY'),
  ('PySide6\\VCRUNTIME140.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\shiboken6\\VCRUNTIME140.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('ucrtbase.dll', 'C:\\Windows\\system32\\ucrtbase.dll', 'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Windows\\system32\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Windows\\system32\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlMeta.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6QmlMeta.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlWorkerScript.dll',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PySide6\\Qt6QmlWorkerScript.dll',
   'BINARY')],
 [],
 [],
 [('config\\app_config.json', 'd:\\code\\OF\\config\\app_config.json', 'DATA'),
  ('config\\matching_config.json',
   'd:\\code\\OF\\config\\matching_config.json',
   'DATA'),
  ('src\\__init__.py', 'd:\\code\\OF\\src\\__init__.py', 'DATA'),
  ('src\\config\\__init__.py',
   'd:\\code\\OF\\src\\config\\__init__.py',
   'DATA'),
  ('src\\config\\config_manager.py',
   'd:\\code\\OF\\src\\config\\config_manager.py',
   'DATA'),
  ('src\\core\\__init__.py', 'd:\\code\\OF\\src\\core\\__init__.py', 'DATA'),
  ('src\\core\\app_launcher.py',
   'd:\\code\\OF\\src\\core\\app_launcher.py',
   'DATA'),
  ('src\\core\\fast_launcher.py',
   'd:\\code\\OF\\src\\core\\fast_launcher.py',
   'DATA'),
  ('src\\logo\\logo.ico', 'd:\\code\\OF\\src\\logo\\logo.ico', 'DATA'),
  ('src\\logo\\logo.png', 'd:\\code\\OF\\src\\logo\\logo.png', 'DATA'),
  ('src\\models\\__init__.py',
   'd:\\code\\OF\\src\\models\\__init__.py',
   'DATA'),
  ('src\\models\\surgery_record.py',
   'd:\\code\\OF\\src\\models\\surgery_record.py',
   'DATA'),
  ('src\\services\\__init__.py',
   'd:\\code\\OF\\src\\services\\__init__.py',
   'DATA'),
  ('src\\services\\data_import_service.py',
   'd:\\code\\OF\\src\\services\\data_import_service.py',
   'DATA'),
  ('src\\services\\export_service.py',
   'd:\\code\\OF\\src\\services\\export_service.py',
   'DATA'),
  ('src\\services\\matching_service.py',
   'd:\\code\\OF\\src\\services\\matching_service.py',
   'DATA'),
  ('src\\ui\\__init__.py', 'd:\\code\\OF\\src\\ui\\__init__.py', 'DATA'),
  ('src\\ui\\about_dialog.py',
   'd:\\code\\OF\\src\\ui\\about_dialog.py',
   'DATA'),
  ('src\\ui\\content_manager.py',
   'd:\\code\\OF\\src\\ui\\content_manager.py',
   'DATA'),
  ('src\\ui\\file_import_widget.py',
   'd:\\code\\OF\\src\\ui\\file_import_widget.py',
   'DATA'),
  ('src\\ui\\help_dialog.py', 'd:\\code\\OF\\src\\ui\\help_dialog.py', 'DATA'),
  ('src\\ui\\main_window.py', 'd:\\code\\OF\\src\\ui\\main_window.py', 'DATA'),
  ('src\\ui\\matching_config_widget.py',
   'd:\\code\\OF\\src\\ui\\matching_config_widget.py',
   'DATA'),
  ('src\\ui\\result_display_widget.py',
   'd:\\code\\OF\\src\\ui\\result_display_widget.py',
   'DATA'),
  ('src\\ui\\stat_card_widget.py',
   'd:\\code\\OF\\src\\ui\\stat_card_widget.py',
   'DATA'),
  ('src\\ui\\styles.py', 'd:\\code\\OF\\src\\ui\\styles.py', 'DATA'),
  ('src\\utils\\__init__.py', 'd:\\code\\OF\\src\\utils\\__init__.py', 'DATA'),
  ('src\\utils\\logger.py', 'd:\\code\\OF\\src\\utils\\logger.py', 'DATA'),
  ('dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\zoneinfo\\dateutil-zoneinfo.tar.gz',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex.tpl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html.tpl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\templates\\html.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_table.tpl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_table.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\html_style.tpl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\templates\\html_style.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_longtable.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\string.tpl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\templates\\string.tpl',
   'DATA'),
  ('pandas\\io\\formats\\templates\\latex_table.tpl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\templates\\latex_table.tpl',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Saratov',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Saratov',
   'DATA'),
  ('pytz\\zoneinfo\\Kwajalein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Sydney',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Sydney',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montevideo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montevideo',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Michigan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Michigan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulaanbaatar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qatar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qatar',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\El_Aaiun',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\El_Aaiun',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Libreville',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Libreville',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Harbin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Harbin',
   'DATA'),
  ('pytz\\zoneinfo\\EST',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\EST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Karachi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Karachi',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaSur',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaSur',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Nicosia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Comoro',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Comoro',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Sakhalin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Sakhalin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Enderbury',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Enderbury',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Apia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Apia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Berlin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Berlin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Campo_Grande',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Campo_Grande',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tunis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tunis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\El_Salvador',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\El_Salvador',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Mountain',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dakar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dakar',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\ACT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\ACT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kampala',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kampala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lower_Princes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lower_Princes',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Oslo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Oslo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\La_Paz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\La_Paz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Winnipeg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Winnipeg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bangui',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bangui',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Rangoon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Rangoon',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Uzhgorod',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Uzhgorod',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Jan_Mayen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Godthab',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Godthab',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belfast',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belfast',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Monterrey',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Monterrey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novosibirsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novosibirsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mendoza',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kashgar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kashgar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grand_Turk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grand_Turk',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-9',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-9',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Eastern',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\London',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\London',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dominica',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dominica',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Broken_Hill',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Broken_Hill',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+10',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Beulah',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Christmas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Christmas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thunder_Bay',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thunder_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-3',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-3',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\West',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\West',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bangkok',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bangkok',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Yap',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Yap',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belize',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belize',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Macquarie',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Macquarie',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Blanc-Sablon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Blanc-Sablon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Wayne',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Wayne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Punta_Arenas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Punta_Arenas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Amsterdam',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Amsterdam',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Adak',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Adak',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Los_Angeles',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Los_Angeles',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lindeman',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lindeman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vatican',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vatican',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Moncton',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Moncton',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Istanbul',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-2',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mazatlan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mazatlan',
   'DATA'),
  ('pytz\\zoneinfo\\HST',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\HST',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dili',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dili',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tokyo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tokyo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Muscat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Muscat',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\East',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\East',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Barbados',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Barbados',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wallis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wallis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Araguaina',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Araguaina',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Lucia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Lucia',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Gibraltar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Gibraltar',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuala_Lumpur',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Beirut',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Beirut',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Vancouver',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Vancouver',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Rothera',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Rothera',
   'DATA'),
  ('pytz\\zoneinfo\\Hongkong',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Hongkong',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qyzylorda',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qyzylorda',
   'DATA'),
  ('pytz\\zoneinfo\\Turkey',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Turkey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guadeloupe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guadeloupe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Denver',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Denver',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Brunei',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Brunei',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Gambier',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Gambier',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Guernsey',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Guernsey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Ushuaia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Saigon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Saigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+7',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+7',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+8',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+8',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ojinaga',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ojinaga',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Blantyre',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Blantyre',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Vostok',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Vostok',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Freetown',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Freetown',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Danmarkshavn',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Danmarkshavn',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guam',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guam',
   'DATA'),
  ('pytz\\zoneinfo\\PST8PDT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\PST8PDT',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Darwin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Darwin',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Novokuznetsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Marengo',
   'DATA'),
  ('pytz\\zoneinfo\\Factory',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Factory',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Shiprock',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Shiprock',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chungking',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chungking',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Auckland',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Auckland',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Queensland',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Queensland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ensenada',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ensenada',
   'DATA'),
  ('pytz\\zoneinfo\\Portugal',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Portugal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montreal',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montreal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rankin_Inlet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rankin_Inlet',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Tripoli',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Tripoli',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bogota',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bogota',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tongatapu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tongatapu',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Johnston',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Johnston',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guatemala',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guatemala',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Caracas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Caracas',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Maldives',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Maldives',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Zulu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\GMT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guyana',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guyana',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Galapagos',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Galapagos',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Samoa',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tortola',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tortola',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Universal',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\EET',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\EET',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Manaus',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Manaus',
   'DATA'),
  ('pytz\\zoneinfo\\Universal',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Universal',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-13',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-13',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Niue',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Niue',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Banjul',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Banjul',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Stockholm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Stockholm',
   'DATA'),
  ('pytz\\zoneinfo\\Greenwich',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Timbuktu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Timbuktu',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mauritius',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mauritius',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmara',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmara',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Khandyga',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Khandyga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia_Banderas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia_Banderas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Volgograd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Volgograd',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuwait',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuwait',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Cape_Verde',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+6',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+6',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tarawa',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tarawa',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Cocos',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Cocos',
   'DATA'),
  ('pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Arctic\\Longyearbyen',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Monaco',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Monaco',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Catamarca',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Catamarca',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yangon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yangon',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kolkata',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kolkata',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\South_Georgia',
   'DATA'),
  ('pytz\\zoneinfo\\leapseconds',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\leapseconds',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chihuahua',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chihuahua',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Riyadh',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Riyadh',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Hermosillo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Hermosillo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atka',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kamchatka',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kamchatka',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dushanbe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dushanbe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jamaica',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Phoenix',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Phoenix',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Atikokan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Atikokan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chatham',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chatham',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Katmandu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Katmandu',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port-au-Prince',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port-au-Prince',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Copenhagen',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Copenhagen',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Syowa',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Syowa',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Stanley',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Stanley',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Rio_Gallegos',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yerevan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yerevan',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Seoul',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Seoul',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\New_Salem',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Matamoros',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Matamoros',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boise',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boise',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Noumea',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Noumea',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lagos',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lagos',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ulan_Bator',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nipigon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nipigon',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-7',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-7',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yekaterinburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimphu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimphu',
   'DATA'),
  ('pytz\\zoneinfo\\UTC',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Calcutta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Calcutta',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yakutat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yakutat',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kyiv',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kyiv',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coyhaique',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coyhaique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mogadishu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mogadishu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ulyanovsk',
   'DATA'),
  ('pytz\\zoneinfo\\CET',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\CET',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tel_Aviv',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+1',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+1',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Niamey',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Niamey',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Louisville',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Madeira',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Madeira',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bucharest',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bucharest',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Davis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Davis',
   'DATA'),
  ('pytz\\zoneinfo\\NZ',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\NZ',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Samoa',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Samoa',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashgabat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashgabat',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Harare',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Harare',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aden',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aden',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bamako',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bamako',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tomsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tomsk',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Algiers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Algiers',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+4',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+4',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nassau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nassau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Tucuman',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santo_Domingo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santo_Domingo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tehran',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tehran',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Yellowknife',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Yellowknife',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Scoresbysund',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Scoresbysund',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Louisville',
   'DATA'),
  ('pytz\\zoneinfo\\zone.tab',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\zone.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Asmera',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Asmera',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Lima',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Lima',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Swift_Current',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Swift_Current',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UCT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Paramaribo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Paramaribo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dhaka',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dhaka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Iqaluit',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Iqaluit',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nome',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nome',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Aleutian',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Aleutian',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Budapest',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Budapest',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Easter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Easter',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Dublin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Dublin',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Chuuk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Chuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kaliningrad',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kaliningrad',
   'DATA'),
  ('pytz\\zoneinfo\\GMT+0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-10',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-10',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Salta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Salta',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Thimbu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Thimbu',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Alaska',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Alaska',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Petersburg',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Winamac',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Whitehorse',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Whitehorse',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Brisbane',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Brisbane',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Makassar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Makassar',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vevay',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+12',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Creston',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Creston',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Abidjan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Abidjan',
   'DATA'),
  ('pytz\\zoneinfo\\GB',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\GB',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Toronto',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Toronto',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kathmandu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kathmandu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\South',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\South',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Barthelemy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Barthelemy',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tiraspol',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tiraspol',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+9',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+9',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Virgin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Virgin',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Tell_City',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Marigot',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Marigot',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Mbabane',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Mbabane',
   'DATA'),
  ('pytz\\zoneinfo\\Singapore',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santarem',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santarem',
   'DATA'),
  ('pytz\\zoneinfo\\GB-Eire',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\GB-Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dubai',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dubai',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\ComodRivadavia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Miquelon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Miquelon',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\DumontDUrville',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Curacao',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Curacao',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jakarta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jakarta',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Chisinau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Chisinau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Juan',
   'DATA'),
  ('pytz\\zoneinfo\\GMT0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Bermuda',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Bermuda',
   'DATA'),
  ('pytz\\zoneinfo\\ROK',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\ROK',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Acre',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\LHI',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\LHI',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Rarotonga',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Rarotonga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cordoba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cordoba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fort_Nelson',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fort_Nelson',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+2',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+2',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Costa_Rica',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Costa_Rica',
   'DATA'),
  ('pytz\\zoneinfo\\zone1970.tab',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\zone1970.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nairobi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nairobi',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kinshasa',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kinshasa',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Perth',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Perth',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Kerguelen',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Kerguelen',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rio_Branco',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rio_Branco',
   'DATA'),
  ('pytz\\zoneinfo\\NZ-CHAT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\NZ-CHAT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ujung_Pandang',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rainy_River',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rainy_River',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\San_Marino',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\San_Marino',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Jersey',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Jersey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Yakutsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Yakutsk',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Lisbon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Lisbon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Porto_Velho',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Porto_Velho',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Porto-Novo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Porto-Novo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Vincent',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Vincent',
   'DATA'),
  ('pytz\\zoneinfo\\Jamaica',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Jamaica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Havana',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Havana',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Central',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cambridge_Bay',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cambridge_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\US\\East-Indiana',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\East-Indiana',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Ljubljana',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Ljubljana',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Addis_Ababa',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Regina',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Regina',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Atyrau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Atyrau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\San_Luis',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Johannesburg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Johannesburg',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Samarkand',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Samarkand',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Samara',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Samara',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faroe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faroe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Ciudad_Juarez',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Norfolk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Norfolk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Choibalsan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Choibalsan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kirov',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kirov',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pohnpei',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pohnpei',
   'DATA'),
  ('pytz\\zoneinfo\\iso3166.tab',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\iso3166.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\NSW',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\NSW',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Eastern',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Eastern',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Adelaide',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Adelaide',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sitka',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sitka',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Grenada',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Grenada',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ceuta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ceuta',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-8',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-8',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Reunion',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Reunion',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Minsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Minsk',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Colombo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Colombo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Panama',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Panama',
   'DATA'),
  ('pytz\\zoneinfo\\MET',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\MET',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Currie',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Currie',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\DeNoronha',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\DeNoronha',
   'DATA'),
  ('pytz\\zoneinfo\\W-SU',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\W-SU',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ouagadougou',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ouagadougou',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Irkutsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Irkutsk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Knox_IN',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Knox_IN',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Luanda',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Luanda',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Nicosia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Nicosia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Urumqi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Urumqi',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mayotte',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mayotte',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tallinn',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tallinn',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Eirunepe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Eirunepe',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Malabo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Malabo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Menominee',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Menominee',
   'DATA'),
  ('pytz\\zoneinfo\\Poland',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Poland',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Ndjamena',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Ndjamena',
   'DATA'),
  ('pytz\\zoneinfo\\PRC',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\PRC',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Casablanca',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Casablanca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Halifax',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Halifax',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\BajaNorte',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\BajaNorte',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Lord_Howe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Lord_Howe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\La_Rioja',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Kiev',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Kiev',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\St_Helena',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\St_Helena',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Antananarivo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Antananarivo',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-11',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-11',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Djibouti',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Djibouti',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Central',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Central',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Isle_of_Man',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vilnius',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vilnius',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Magadan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Magadan',
   'DATA'),
  ('pytz\\zoneinfo\\ROC',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\ROC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Amman',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Amman',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Phnom_Penh',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Atlantic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Atlantic',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Honolulu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Honolulu',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Moscow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Moscow',
   'DATA'),
  ('pytz\\zoneinfo\\Japan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Japan',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Saipan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Saipan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sarajevo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sarajevo',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Maceio',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Maceio',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-4',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-4',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Almaty',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Almaty',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Belgrade',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Belgrade',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vaduz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vaduz',
   'DATA'),
  ('pytz\\zoneinfo\\America\\New_York',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\New_York',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Boa_Vista',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Boa_Vista',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Pacific',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Detroit',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Detroit',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Brussels',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Brussels',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Edmonton',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Edmonton',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Juba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Juba',
   'DATA'),
  ('pytz\\zoneinfo\\Israel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Israel',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Helsinki',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Helsinki',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Nouakchott',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Nouakchott',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Bratislava',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Bratislava',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Marquesas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Marquesas',
   'DATA'),
  ('pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\North_Dakota\\Center',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lusaka',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lusaka',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Skopje',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Skopje',
   'DATA'),
  ('pytz\\zoneinfo\\Libya',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Libya',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Hawaii',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Hawaii',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Johns',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Johns',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayman',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayman',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zagreb',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zagreb',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Resolute',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Resolute',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Dar_es_Salaam',
   'DATA'),
  ('pytz\\zoneinfo\\tzdata.zi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\tzdata.zi',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Macao',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Macao',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Azores',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Azores',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hebron',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hebron',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Aruba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Aruba',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Cairo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Cairo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Kigali',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Kigali',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Prague',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Prague',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Coral_Harbour',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Coral_Harbour',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Wake',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Wake',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Majuro',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Majuro',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fiji',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fiji',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Monrovia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Monrovia',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Arizona',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Arizona',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-1',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-1',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Buenos_Aires',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Martinique',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Martinique',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Sao_Tome',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Sao_Tome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Vincennes',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+3',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+3',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ashkhabad',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ashkhabad',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tbilisi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tbilisi',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Yukon',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Yukon',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cancun',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cancun',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Hobart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Hobart',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Asuncion',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Asuncion',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kanton',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kanton',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\South_Pole',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\South_Pole',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Omsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Omsk',
   'DATA'),
  ('pytz\\zoneinfo\\MST7MDT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\MST7MDT',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Istanbul',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Istanbul',
   'DATA'),
  ('pytz\\zoneinfo\\GMT-0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\GMT-0',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Sofia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Sofia',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Dacca',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Dacca',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Fortaleza',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Fortaleza',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Antigua',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Antigua',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Chagos',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Chagos',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Casey',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Casey',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Manila',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Manila',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zaporozhye',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zaporozhye',
   'DATA'),
  ('pytz\\zoneinfo\\WET',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\WET',
   'DATA'),
  ('pytz\\zoneinfo\\Eire',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Eire',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Astrakhan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Astrakhan',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Vienna',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Vienna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Guayaquil',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Guayaquil',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Riga',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Riga',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Rosario',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Rosario',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kwajalein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kwajalein',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Mountain',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Mountain',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tijuana',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tijuana',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vladivostok',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vladivostok',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indianapolis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Merida',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Merida',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Brazzaville',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Brazzaville',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Tasmania',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Tasmania',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Athens',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Athens',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Yancowinna',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Yancowinna',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Bahia',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Bahia',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cayenne',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cayenne',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Mawson',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Mawson',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Rome',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Rome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Cuiaba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Cuiaba',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santiago',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santiago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Pangnirtung',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Pangnirtung',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\Continental',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\Continental',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Inuvik',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Inuvik',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kiritimati',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kiritimati',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Kosrae',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Kosrae',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Eucla',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Eucla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Dawson_Creek',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Dawson_Creek',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\UTC',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\UTC',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Vientiane',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Vientiane',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bissau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bissau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Tashkent',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Tashkent',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+11',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+11',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Glace_Bay',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Glace_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Warsaw',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Warsaw',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Sao_Paulo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Sao_Paulo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Accra',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Accra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Thule',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Thule',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Thomas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Thomas',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Paris',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Paris',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Saskatchewan',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Saskatchewan',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-5',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-5',
   'DATA'),
  ('pytz\\zoneinfo\\Iran',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Iran',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Podgorica',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Podgorica',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Chicago',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Chicago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Knox',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Knox',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Gaza',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Gaza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jayapura',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jayapura',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Tegucigalpa',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Tegucigalpa',
   'DATA'),
  ('pytz\\zoneinfo\\Iceland',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Iceland',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hong_Kong',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hong_Kong',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Managua',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Managua',
   'DATA'),
  ('pytz\\zoneinfo\\zonenow.tab',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\zonenow.tab',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Hovd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Hovd',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Buenos_Aires',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Melbourne',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Melbourne',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anguilla',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anguilla',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Argentina\\Mendoza',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ust-Nera',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ust-Nera',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+0',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Srednekolymsk',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Reykjavik',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Ho_Chi_Minh',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Barnaul',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Barnaul',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Palmer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Palmer',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Canary',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Canary',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Windhoek',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Windhoek',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Luxembourg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Luxembourg',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Khartoum',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Khartoum',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pyongyang',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pyongyang',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Anadyr',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Anadyr',
   'DATA'),
  ('pytz\\zoneinfo\\Cuba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Cuba',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Oral',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Oral',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Guadalcanal',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-14',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-14',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Taipei',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Taipei',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Conakry',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Conakry',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Tirane',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Tirane',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kentucky\\Monticello',
   'DATA'),
  ('pytz\\zoneinfo\\MST',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\MST',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lubumbashi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lubumbashi',
   'DATA'),
  ('pytz\\zoneinfo\\UCT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\UCT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Jerusalem',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Jerusalem',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maseru',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maseru',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Simferopol',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Simferopol',
   'DATA'),
  ('pytz\\zoneinfo\\America\\St_Kitts',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\St_Kitts',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Lome',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Lome',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Santa_Isabel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Santa_Isabel',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Nauru',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Nauru',
   'DATA'),
  ('pytz\\zoneinfo\\Zulu',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Zulu',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Canberra',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Canberra',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Anchorage',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Anchorage',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Montserrat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Montserrat',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Nuuk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Nuuk',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Bougainville',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Bougainville',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Efate',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Efate',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Funafuti',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Funafuti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Famagusta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Famagusta',
   'DATA'),
  ('pytz\\zoneinfo\\Chile\\EasterIsland',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Chile\\EasterIsland',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Mexico_City',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Mexico_City',
   'DATA'),
  ('pytz\\zoneinfo\\Atlantic\\Faeroe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Atlantic\\Faeroe',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bishkek',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bishkek',
   'DATA'),
  ('pytz\\zoneinfo\\CST6CDT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\CST6CDT',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Douala',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Douala',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-12',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-12',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Juneau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Juneau',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Jujuy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Jujuy',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Metlakatla',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Metlakatla',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\West',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\West',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Goose_Bay',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Goose_Bay',
   'DATA'),
  ('pytz\\zoneinfo\\Navajo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Navajo',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Palau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Palau',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\Victoria',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\Victoria',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Ponape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Ponape',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\Greenwich',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\Greenwich',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Midway',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Midway',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Krasnoyarsk',
   'DATA'),
  ('pytz\\zoneinfo\\EST5EDT',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\EST5EDT',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kuching',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kuching',
   'DATA'),
  ('pytz\\zoneinfo\\US\\Indiana-Starke',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\US\\Indiana-Starke',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtobe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtobe',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Noronha',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Noronha',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Recife',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Recife',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Madrid',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Madrid',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Port_Moresby',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pitcairn',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pitcairn',
   'DATA'),
  ('pytz\\zoneinfo\\Australia\\North',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Australia\\North',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Pacific',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Pacific',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baghdad',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baghdad',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Bujumbura',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Bujumbura',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT-6',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT-6',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\Troll',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\Troll',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Pontianak',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Pontianak',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Zurich',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Zurich',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chita',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chita',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Kralendijk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Kralendijk',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Belem',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Belem',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Chongqing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Chongqing',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Damascus',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Damascus',
   'DATA'),
  ('pytz\\zoneinfo\\Antarctica\\McMurdo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Antarctica\\McMurdo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Qostanay',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Qostanay',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Indiana\\Indianapolis',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Mariehamn',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Mariehamn',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Maputo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Maputo',
   'DATA'),
  ('pytz\\zoneinfo\\Africa\\Gaborone',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Africa\\Gaborone',
   'DATA'),
  ('pytz\\zoneinfo\\Canada\\Newfoundland',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Canada\\Newfoundland',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Malta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Malta',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Pago_Pago',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Puerto_Rico',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Puerto_Rico',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Truk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Truk',
   'DATA'),
  ('pytz\\zoneinfo\\Egypt',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Egypt',
   'DATA'),
  ('pytz\\zoneinfo\\America\\Port_of_Spain',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\America\\Port_of_Spain',
   'DATA'),
  ('pytz\\zoneinfo\\Indian\\Mahe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Indian\\Mahe',
   'DATA'),
  ('pytz\\zoneinfo\\Etc\\GMT+5',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Etc\\GMT+5',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Andorra',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Andorra',
   'DATA'),
  ('pytz\\zoneinfo\\Europe\\Busingen',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Europe\\Busingen',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Bahrain',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Bahrain',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Tahiti',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Tahiti',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Singapore',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Singapore',
   'DATA'),
  ('pytz\\zoneinfo\\Mexico\\General',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Mexico\\General',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Baku',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Baku',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Shanghai',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Shanghai',
   'DATA'),
  ('pytz\\zoneinfo\\Brazil\\Acre',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Brazil\\Acre',
   'DATA'),
  ('pytz\\zoneinfo\\Pacific\\Fakaofo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Pacific\\Fakaofo',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Aqtau',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Aqtau',
   'DATA'),
  ('pytz\\zoneinfo\\Asia\\Kabul',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\zoneinfo\\Asia\\Kabul',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ka.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ka.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ka.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_ka.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('base_library.zip',
   'd:\\code\\OF\\build\\手术费用匹配系统\\base_library.zip',
   'DATA')],
 [('weakref', 'D:\\Python\\Python310\\lib\\weakref.py', 'PYMODULE'),
  ('collections.abc',
   'D:\\Python\\Python310\\lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'D:\\Python\\Python310\\lib\\collections\\__init__.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'D:\\Python\\Python310\\lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'D:\\Python\\Python310\\lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'D:\\Python\\Python310\\lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'D:\\Python\\Python310\\lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'D:\\Python\\Python310\\lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'D:\\Python\\Python310\\lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'D:\\Python\\Python310\\lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'D:\\Python\\Python310\\lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'D:\\Python\\Python310\\lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'D:\\Python\\Python310\\lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'D:\\Python\\Python310\\lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'D:\\Python\\Python310\\lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'D:\\Python\\Python310\\lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'D:\\Python\\Python310\\lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'D:\\Python\\Python310\\lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'D:\\Python\\Python310\\lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'D:\\Python\\Python310\\lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'D:\\Python\\Python310\\lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'D:\\Python\\Python310\\lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'D:\\Python\\Python310\\lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'D:\\Python\\Python310\\lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'D:\\Python\\Python310\\lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'D:\\Python\\Python310\\lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'D:\\Python\\Python310\\lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'D:\\Python\\Python310\\lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'D:\\Python\\Python310\\lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'D:\\Python\\Python310\\lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'D:\\Python\\Python310\\lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'D:\\Python\\Python310\\lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'D:\\Python\\Python310\\lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'D:\\Python\\Python310\\lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'D:\\Python\\Python310\\lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'D:\\Python\\Python310\\lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'D:\\Python\\Python310\\lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'D:\\Python\\Python310\\lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'D:\\Python\\Python310\\lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'D:\\Python\\Python310\\lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'D:\\Python\\Python310\\lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'D:\\Python\\Python310\\lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'D:\\Python\\Python310\\lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'D:\\Python\\Python310\\lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'D:\\Python\\Python310\\lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'D:\\Python\\Python310\\lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'D:\\Python\\Python310\\lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'D:\\Python\\Python310\\lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'D:\\Python\\Python310\\lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'D:\\Python\\Python310\\lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'D:\\Python\\Python310\\lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'D:\\Python\\Python310\\lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'D:\\Python\\Python310\\lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz', 'D:\\Python\\Python310\\lib\\encodings\\hz.py', 'PYMODULE'),
  ('encodings.hp_roman8',
   'D:\\Python\\Python310\\lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'D:\\Python\\Python310\\lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'D:\\Python\\Python310\\lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'D:\\Python\\Python310\\lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'D:\\Python\\Python310\\lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'D:\\Python\\Python310\\lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'D:\\Python\\Python310\\lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'D:\\Python\\Python310\\lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'D:\\Python\\Python310\\lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'D:\\Python\\Python310\\lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'D:\\Python\\Python310\\lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'D:\\Python\\Python310\\lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'D:\\Python\\Python310\\lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'D:\\Python\\Python310\\lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'D:\\Python\\Python310\\lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'D:\\Python\\Python310\\lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'D:\\Python\\Python310\\lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'D:\\Python\\Python310\\lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'D:\\Python\\Python310\\lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'D:\\Python\\Python310\\lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'D:\\Python\\Python310\\lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'D:\\Python\\Python310\\lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'D:\\Python\\Python310\\lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'D:\\Python\\Python310\\lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'D:\\Python\\Python310\\lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'D:\\Python\\Python310\\lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'D:\\Python\\Python310\\lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'D:\\Python\\Python310\\lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'D:\\Python\\Python310\\lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'D:\\Python\\Python310\\lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'D:\\Python\\Python310\\lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'D:\\Python\\Python310\\lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'D:\\Python\\Python310\\lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'D:\\Python\\Python310\\lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'D:\\Python\\Python310\\lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'D:\\Python\\Python310\\lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'D:\\Python\\Python310\\lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'D:\\Python\\Python310\\lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'D:\\Python\\Python310\\lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'D:\\Python\\Python310\\lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'D:\\Python\\Python310\\lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'D:\\Python\\Python310\\lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'D:\\Python\\Python310\\lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'D:\\Python\\Python310\\lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'D:\\Python\\Python310\\lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'D:\\Python\\Python310\\lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'D:\\Python\\Python310\\lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'D:\\Python\\Python310\\lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'D:\\Python\\Python310\\lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'D:\\Python\\Python310\\lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'D:\\Python\\Python310\\lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'D:\\Python\\Python310\\lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'D:\\Python\\Python310\\lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'D:\\Python\\Python310\\lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'D:\\Python\\Python310\\lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'D:\\Python\\Python310\\lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'D:\\Python\\Python310\\lib\\encodings\\__init__.py',
   'PYMODULE'),
  ('warnings', 'D:\\Python\\Python310\\lib\\warnings.py', 'PYMODULE'),
  ('traceback', 'D:\\Python\\Python310\\lib\\traceback.py', 'PYMODULE'),
  ('codecs', 'D:\\Python\\Python310\\lib\\codecs.py', 'PYMODULE'),
  ('reprlib', 'D:\\Python\\Python310\\lib\\reprlib.py', 'PYMODULE'),
  ('re', 'D:\\Python\\Python310\\lib\\re.py', 'PYMODULE'),
  ('types', 'D:\\Python\\Python310\\lib\\types.py', 'PYMODULE'),
  ('operator', 'D:\\Python\\Python310\\lib\\operator.py', 'PYMODULE'),
  ('functools', 'D:\\Python\\Python310\\lib\\functools.py', 'PYMODULE'),
  ('os', 'D:\\Python\\Python310\\lib\\os.py', 'PYMODULE'),
  ('sre_compile', 'D:\\Python\\Python310\\lib\\sre_compile.py', 'PYMODULE'),
  ('io', 'D:\\Python\\Python310\\lib\\io.py', 'PYMODULE'),
  ('abc', 'D:\\Python\\Python310\\lib\\abc.py', 'PYMODULE'),
  ('heapq', 'D:\\Python\\Python310\\lib\\heapq.py', 'PYMODULE'),
  ('sre_parse', 'D:\\Python\\Python310\\lib\\sre_parse.py', 'PYMODULE'),
  ('sre_constants', 'D:\\Python\\Python310\\lib\\sre_constants.py', 'PYMODULE'),
  ('_collections_abc',
   'D:\\Python\\Python310\\lib\\_collections_abc.py',
   'PYMODULE'),
  ('enum', 'D:\\Python\\Python310\\lib\\enum.py', 'PYMODULE'),
  ('stat', 'D:\\Python\\Python310\\lib\\stat.py', 'PYMODULE'),
  ('posixpath', 'D:\\Python\\Python310\\lib\\posixpath.py', 'PYMODULE'),
  ('locale', 'D:\\Python\\Python310\\lib\\locale.py', 'PYMODULE'),
  ('keyword', 'D:\\Python\\Python310\\lib\\keyword.py', 'PYMODULE'),
  ('genericpath', 'D:\\Python\\Python310\\lib\\genericpath.py', 'PYMODULE'),
  ('ntpath', 'D:\\Python\\Python310\\lib\\ntpath.py', 'PYMODULE'),
  ('linecache', 'D:\\Python\\Python310\\lib\\linecache.py', 'PYMODULE'),
  ('_weakrefset', 'D:\\Python\\Python310\\lib\\_weakrefset.py', 'PYMODULE'),
  ('copyreg', 'D:\\Python\\Python310\\lib\\copyreg.py', 'PYMODULE')])
