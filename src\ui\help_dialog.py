#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
帮助对话框
显示详细的使用说明、操作指南和常见问题解答
"""
import sys
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QTabWidget, QWidget,
    QScrollArea, QFrame, QTreeWidget, QTreeWidgetItem,
    QSplitter
)
from PySide6.QtCore import Qt, QUrl
from PySide6.QtGui import QPixmap, QIcon, QFont, QDesktopServices

from .styles import ModernStyle
from .content_manager import ContentManager


class HelpDialog(QDialog):
    """帮助对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("帮助 - 手术费用匹配系统")
        self.resize(800, 600)  # 默认大小
        self.setMinimumSize(600, 450)  # 最小尺寸
        self.setWindowFlags(Qt.Dialog | Qt.WindowCloseButtonHint)
        
        # 设置图标
        self._set_window_icon()
        
        # 设置样式
        self.setStyleSheet(ModernStyle.get_dialog_style())
        
        # 创建界面
        self._create_ui()
        
        # 居中显示
        self._center_on_parent()
        
        # 保存窗口状态
        self._load_window_state()
    
    def _set_window_icon(self):
        """设置窗口图标"""
        try:
            # 获取图标路径
            if getattr(sys, 'frozen', False):
                base_path = Path(sys._MEIPASS)
            else:
                base_path = Path(__file__).parent.parent.parent
            
            icon_paths = [
                base_path / "src" / "logo" / "logo.ico",
                base_path / "src" / "logo" / "logo.png",
                Path("src") / "logo" / "logo.ico",
                Path("src") / "logo" / "logo.png",
            ]
            
            for icon_path in icon_paths:
                if icon_path.exists():
                    self.setWindowIcon(QIcon(str(icon_path)))
                    return
        except Exception:
            pass
    
    def _create_ui(self):
        """创建用户界面"""
        layout = QVBoxLayout()
        layout.setSpacing(0)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧导航树
        nav_tree = self._create_navigation_tree()
        nav_tree.setMaximumWidth(250)
        nav_tree.setMinimumWidth(200)
        
        # 右侧内容区域
        content_area = self._create_content_area()
        
        splitter.addWidget(nav_tree)
        splitter.addWidget(content_area)
        splitter.setStretchFactor(0, 0)  # 导航树不拉伸
        splitter.setStretchFactor(1, 1)  # 内容区域可拉伸
        
        layout.addWidget(splitter)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        close_btn = QPushButton("关闭")
        close_btn.setStyleSheet("""
            QPushButton {
                background: #2196F3;
                color: white;
                border: none;
                padding: 10px 30px;
                border-radius: 6px;
                font-size: 14px;
                font-weight: bold;
            }
            QPushButton:hover {
                background: #1976D2;
            }
            QPushButton:pressed {
                background: #1565C0;
            }
        """)
        close_btn.clicked.connect(self.accept)
        
        button_layout.addWidget(close_btn)
        button_layout.setContentsMargins(20, 10, 20, 20)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def _create_navigation_tree(self):
        """创建导航树"""
        tree = QTreeWidget()
        tree.setHeaderHidden(True)
        tree.setStyleSheet("""
            QTreeWidget {
                background: #f8f9fa;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                font-size: 13px;
            }
            QTreeWidget::item {
                padding: 8px;
                border-bottom: 1px solid #f0f0f0;
            }
            QTreeWidget::item:selected {
                background: #e3f2fd;
                color: #1976D2;
            }
            QTreeWidget::item:hover {
                background: #f0f8ff;
            }
        """)
        
        # 创建导航项目
        nav_items = [
            ("🚀 快速开始", [
                "系统概述",
                "安装要求", 
                "首次使用"
            ]),
            ("📊 数据导入", [
                "支持格式",
                "导入步骤",
                "数据预览",
                "常见问题"
            ]),
            ("🎯 数据匹配", [
                "匹配原理",
                "参数配置",
                "执行匹配",
                "结果分析"
            ]),
            ("📤 结果导出", [
                "导出格式",
                "导出选项",
                "文件保存",
                "批量处理"
            ]),
            ("⚙️ 高级功能", [
                "自定义配置",
                "批量操作",
                "性能优化",
                "插件扩展"
            ]),
            ("❓ 常见问题", [
                "导入问题",
                "匹配问题", 
                "导出问题",
                "性能问题"
            ]),
            ("🔧 故障排除", [
                "错误代码",
                "日志分析",
                "联系支持",
                "更新升级"
            ])
        ]
        
        for category, items in nav_items:
            category_item = QTreeWidgetItem(tree, [category])
            category_item.setExpanded(True)
            
            for item in items:
                child_item = QTreeWidgetItem(category_item, [item])
        
        # 连接选择事件
        tree.itemClicked.connect(self._on_nav_item_clicked)
        
        # 默认选择第一项
        if tree.topLevelItemCount() > 0:
            first_category = tree.topLevelItem(0)
            if first_category.childCount() > 0:
                tree.setCurrentItem(first_category.child(0))
        
        return tree
    
    def _create_content_area(self):
        """创建内容区域"""
        # 创建滚动区域
        scroll = QScrollArea()
        scroll.setWidgetResizable(True)
        scroll.setStyleSheet("QScrollArea { border: none; background: white; }")
        
        # 内容文本区域
        self.content_text = QTextEdit()
        self.content_text.setReadOnly(True)
        self.content_text.setStyleSheet("""
            QTextEdit {
                border: none;
                background: white;
                font-size: 14px;
                line-height: 1.6;
                padding: 20px;
            }
        """)
        
        # 设置默认内容
        self._load_content("系统概述")
        
        scroll.setWidget(self.content_text)
        return scroll
    
    def _on_nav_item_clicked(self, item, column):
        """导航项目点击事件"""
        if item.parent():  # 只处理子项目
            topic = item.text(0)
            self._load_content(topic)
    
    def _load_content(self, topic):
        """加载内容"""
        content_map = {
            "系统概述": self._get_quick_start_content(),
            "安装要求": self._get_requirements_content(),
            "首次使用": self._get_first_use_content(),
            "支持格式": self._get_formats_content(),
            "导入步骤": self._get_import_steps_content(),
            "数据预览": self._get_preview_content(),
            "匹配原理": self._get_matching_principle_content(),
            "参数配置": self._get_config_content(),
            "执行匹配": self._get_execute_matching_content(),
            "结果分析": self._get_result_analysis_content(),
            "导出格式": self._get_export_formats_content(),
            "导出选项": self._get_export_options_content(),
            "常见问题": self._get_faq_content(),
            "故障排除": self._get_troubleshooting_content(),
        }
        
        content = content_map.get(topic, self._get_default_content(topic))
        self.content_text.setHtml(content)
    
    def _get_quick_start_content(self):
        """快速开始内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        🚀 5分钟快速上手指南
        </h1>

        <div style="background: #e8f5e8; padding: 20px; border-left: 4px solid #4caf50; margin: 15px 0; border-radius: 8px;">
            <h2 style="color: #2e7d32; margin-top: 0;">✅ 完整操作流程</h2>
            <p style="font-size: 16px; margin-bottom: 0;"><b>只需6个步骤，轻松完成数据匹配！</b></p>
        </div>

        <h2 style="color: #2196F3;">第1步：准备数据文件</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <p><b>表一（手术记录）</b>需要包含：</p>
            <ul style="margin: 5px 0;">
                <li>姓名、住院号、手术日期、手术名称、费用</li>
            </ul>
            <p><b>表二（费用记录）</b>需要包含：</p>
            <ul style="margin: 5px 0;">
                <li>姓名、住院号、费用日期、费用项目、费用</li>
            </ul>
            <p style="color: #666; font-size: 13px; margin-top: 10px;">💡 支持Excel (.xlsx, .xls) 和 CSV 格式</p>
        </div>

        <h2 style="color: #2196F3;">第2步：导入表一数据</h2>
        <ol style="line-height: 1.8; background: #f0f8ff; padding: 15px; border-radius: 8px;">
            <li>点击左侧面板的 <b>"导入表一"</b> 按钮</li>
            <li>选择手术记录文件</li>
            <li>如果是Excel文件，选择正确的工作表</li>
            <li>等待导入完成，查看预览数据</li>
        </ol>

        <h2 style="color: #2196F3;">第3步：导入表二数据</h2>
        <ol style="line-height: 1.8; background: #fff3e0; padding: 15px; border-radius: 8px;">
            <li>点击左侧面板的 <b>"导入表二"</b> 按钮</li>
            <li>选择费用记录文件</li>
            <li>确认数据导入正确</li>
        </ol>

        <h2 style="color: #2196F3;">第4步：配置匹配参数（可选）</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <p>系统提供默认的最优参数，通常无需调整。如需自定义：</p>
            <ul style="line-height: 1.6;">
                <li><b>姓名匹配阈值</b>：建议80-90%（处理输入错误）</li>
                <li><b>日期容差</b>：建议±3天（处理日期差异）</li>
                <li><b>住院号匹配</b>：建议启用精确匹配</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">第5步：执行匹配</h2>
        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px;">
            <ol style="line-height: 1.8; margin: 0;">
                <li>点击 <b>"开始匹配"</b> 按钮</li>
                <li>观察进度条和状态信息</li>
                <li>等待匹配完成（通常几秒到几分钟）</li>
            </ol>
        </div>

        <h2 style="color: #2196F3;">第6步：查看和导出结果</h2>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px;">
            <ul style="line-height: 1.8; margin: 0;">
                <li><b>查看匹配统计</b>：成功匹配数量、未匹配数量</li>
                <li><b>预览匹配结果</b>：检查匹配质量</li>
                <li><b>导出结果</b>：点击"导出结果"保存为Excel文件</li>
            </ul>
        </div>

        <div style="background: #fff3e0; padding: 20px; border-left: 4px solid #ff9800; margin: 20px 0; border-radius: 8px;">
            <h3 style="color: #f57c00; margin-top: 0;">⚡ 快速提示</h3>
            <ul style="line-height: 1.8; margin-bottom: 0;">
                <li><b>数据质量</b>：数据越规范，匹配效果越好</li>
                <li><b>文件大小</b>：建议单文件不超过10万条记录</li>
                <li><b>匹配时间</b>：1万条记录约需10-30秒</li>
                <li><b>结果验证</b>：建议抽查部分结果确认准确性</li>
            </ul>
        </div>

        <div style="background: #e8f5e8; padding: 20px; border-left: 4px solid #4caf50; margin: 20px 0; border-radius: 8px;">
            <h3 style="color: #2e7d32; margin-top: 0;">🎯 成功秘诀</h3>
            <p style="margin-bottom: 0;"><b>第一次使用建议：</b>先用小量数据（100-1000条）测试，熟悉流程后再处理大批量数据。</p>
        </div>
        """
    
    def _get_requirements_content(self):
        """安装要求内容"""
        return ContentManager.get_detailed_requirements_html()
    
    def _get_first_use_content(self):
        """首次使用内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        🚀 首次使用指南
        </h1>
        
        <h2 style="color: #2196F3;">第一步：启动程序</h2>
        <ol style="line-height: 1.8;">
            <li>双击桌面上的"手术费用匹配系统"图标</li>
            <li>等待程序启动（通常1-3秒）</li>
            <li>查看启动画面的加载进度</li>
            <li>程序主界面出现后即可开始使用</li>
        </ol>
        
        <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0;">
            <p><b>💡 启动提示</b></p>
            <p>首次启动可能需要稍长时间，后续启动会更快。如果启动缓慢，请检查杀毒软件设置。</p>
        </div>
        
        <h2 style="color: #2196F3;">第二步：界面介绍</h2>
        <h3>主界面布局</h3>
        <ul style="line-height: 1.8;">
            <li><b>左侧面板</b>：数据导入和配置区域</li>
            <li><b>中央区域</b>：数据预览和匹配结果显示</li>
            <li><b>底部状态栏</b>：显示当前操作状态和统计信息</li>
            <li><b>顶部菜单栏</b>：文件操作、工具和帮助菜单</li>
        </ul>
        
        <h2 style="color: #2196F3;">第三步：准备数据</h2>
        <h3>数据格式要求</h3>
        <div style="background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0;">
            <p><b>表一数据（手术记录）应包含：</b></p>
            <ul>
                <li>姓名</li>
                <li>住院号</li>
                <li>手术日期</li>
                <li>手术名称</li>
                <li>费用金额</li>
            </ul>
        </div>
        
        <div style="background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
            <p><b>表二数据（费用记录）应包含：</b></p>
            <ul>
                <li>姓名</li>
                <li>住院号</li>
                <li>费用日期</li>
                <li>费用项目</li>
                <li>费用金额</li>
            </ul>
        </div>
        
        <h2 style="color: #2196F3;">第四步：快速匹配</h2>
        <ol style="line-height: 1.8;">
            <li><b>导入表一</b>：点击"导入表一"按钮，选择手术记录文件</li>
            <li><b>导入表二</b>：点击"导入表二"按钮，选择费用记录文件</li>
            <li><b>预览数据</b>：检查导入的数据是否正确</li>
            <li><b>开始匹配</b>：点击"开始匹配"按钮</li>
            <li><b>查看结果</b>：在结果区域查看匹配情况</li>
            <li><b>导出结果</b>：点击"导出结果"保存匹配结果</li>
        </ol>
        
        <h2 style="color: #2196F3;">常用快捷键</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">快捷键</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">功能</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Ctrl + O</td>
                <td style="padding: 10px; border: 1px solid #ddd;">打开文件</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Ctrl + S</td>
                <td style="padding: 10px; border: 1px solid #ddd;">保存结果</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">F1</td>
                <td style="padding: 10px; border: 1px solid #ddd;">打开帮助</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Ctrl + Q</td>
                <td style="padding: 10px; border: 1px solid #ddd;">退出程序</td>
            </tr>
        </table>
        """

    def _get_preview_content(self):
        """数据预览内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        👀 数据预览功能使用
        </h1>

        <h2 style="color: #2196F3;">预览界面说明</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <p>数据导入后，系统会在中央区域显示数据预览：</p>
            <ul style="line-height: 1.8;">
                <li><b>表一预览</b>：显示手术记录的前几行数据</li>
                <li><b>表二预览</b>：显示费用记录的前几行数据</li>
                <li><b>列标题</b>：显示识别到的字段名称</li>
                <li><b>数据内容</b>：显示实际的数据值</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">如何检查数据质量</h2>
        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>✅ 检查要点</h3>
            <ol style="line-height: 1.8;">
                <li><b>字段识别</b>：确认姓名、住院号等关键字段被正确识别</li>
                <li><b>数据格式</b>：检查日期格式是否统一</li>
                <li><b>数据完整性</b>：确认关键字段没有大量空值</li>
                <li><b>编码问题</b>：确认中文显示正常，无乱码</li>
            </ol>
        </div>

        <h2 style="color: #2196F3;">常见预览问题</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">问题</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">解决方法</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">字段识别错误</td>
                <td style="padding: 10px; border: 1px solid #ddd;">重新导入，或手动调整列标题</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">中文乱码</td>
                <td style="padding: 10px; border: 1px solid #ddd;">尝试不同编码格式导入</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">数据不完整</td>
                <td style="padding: 10px; border: 1px solid #ddd;">检查原始文件，补充缺失数据</td>
            </tr>
        </table>
        """

    def _get_matching_principle_content(self):
        """匹配原理内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        🎯 数据匹配原理与策略
        </h1>

        <h2 style="color: #2196F3;">匹配算法说明</h2>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <p>系统采用<b>多字段组合匹配</b>策略，确保高准确率：</p>
            <ol style="line-height: 1.8;">
                <li><b>主键匹配</b>：优先使用住院号进行精确匹配</li>
                <li><b>姓名匹配</b>：使用模糊匹配算法处理输入错误</li>
                <li><b>日期匹配</b>：在指定容差范围内匹配日期</li>
                <li><b>综合评分</b>：多个条件综合评分，选择最佳匹配</li>
            </ol>
        </div>

        <h2 style="color: #2196F3;">匹配优先级</h2>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>🥇 第一优先级：住院号 + 姓名</h3>
            <p>住院号完全相同且姓名相似度≥阈值</p>

            <h3>🥈 第二优先级：姓名 + 日期</h3>
            <p>姓名相似度≥阈值且日期在容差范围内</p>

            <h3>🥉 第三优先级：单一字段</h3>
            <p>仅住院号匹配或仅姓名高度相似</p>
        </div>

        <h2 style="color: #2196F3;">匹配参数说明</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">参数</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">默认值</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">说明</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">姓名相似度阈值</td>
                <td style="padding: 10px; border: 1px solid #ddd;">85%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">低于此值不认为匹配</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">日期容差</td>
                <td style="padding: 10px; border: 1px solid #ddd;">±3天</td>
                <td style="padding: 10px; border: 1px solid #ddd;">允许的日期差异范围</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">住院号匹配</td>
                <td style="padding: 10px; border: 1px solid #ddd;">精确</td>
                <td style="padding: 10px; border: 1px solid #ddd;">必须完全相同</td>
            </tr>
        </table>
        """

    def _get_config_content(self):
        """参数配置内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        ⚙️ 匹配参数配置指南
        </h1>

        <h2 style="color: #2196F3;">何时需要调整参数</h2>
        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <ul style="line-height: 1.8;">
                <li><b>匹配率过低</b>：明显应该匹配的记录没有匹配上</li>
                <li><b>误匹配过多</b>：不相关的记录被错误匹配</li>
                <li><b>数据质量差</b>：存在大量输入错误或格式不统一</li>
                <li><b>特殊需求</b>：有特定的业务匹配要求</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">参数调整建议</h2>

        <h3>📝 姓名匹配阈值调整</h3>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <ul style="line-height: 1.8;">
                <li><b>提高阈值（90-95%）</b>：数据质量好，减少误匹配</li>
                <li><b>降低阈值（70-80%）</b>：存在输入错误，提高匹配率</li>
                <li><b>默认值（85%）</b>：适合大多数情况</li>
            </ul>
        </div>

        <h3>📅 日期容差调整</h3>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <ul style="line-height: 1.8;">
                <li><b>±1天</b>：日期记录准确，严格匹配</li>
                <li><b>±3天</b>：默认设置，适合一般情况</li>
                <li><b>±7天</b>：日期记录可能有较大差异</li>
                <li><b>±30天</b>：按月匹配，适合特殊需求</li>
            </ul>
        </div>

        <h3>🏥 住院号匹配策略</h3>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <ul style="line-height: 1.8;">
                <li><b>精确匹配</b>：推荐设置，住院号必须完全相同</li>
                <li><b>模糊匹配</b>：住院号可能有格式差异时使用</li>
                <li><b>忽略住院号</b>：住院号不可靠时，仅用姓名和日期</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">配置操作步骤</h2>
        <ol style="line-height: 1.8; background: #e3f2fd; padding: 15px; border-radius: 8px;">
            <li>在左侧面板找到"匹配配置"区域</li>
            <li>根据数据特点调整相应参数</li>
            <li>点击"应用配置"保存设置</li>
            <li>执行匹配测试效果</li>
            <li>根据结果进一步微调参数</li>
        </ol>
        """

    def _get_execute_matching_content(self):
        """执行匹配内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        ▶️ 执行匹配操作指南
        </h1>

        <h2 style="color: #2196F3;">匹配前检查清单</h2>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>✅ 确认以下项目</h3>
            <ul style="line-height: 1.8;">
                <li>表一和表二数据都已正确导入</li>
                <li>数据预览显示正常，无乱码</li>
                <li>关键字段（姓名、住院号）被正确识别</li>
                <li>匹配参数已根据需要调整</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">执行匹配步骤</h2>
        <ol style="line-height: 1.8; background: #f0f8ff; padding: 15px; border-radius: 8px;">
            <li><b>点击"开始匹配"按钮</b></li>
            <li><b>观察进度显示</b>：
                <ul style="margin: 5px 0;">
                    <li>进度条显示完成百分比</li>
                    <li>状态文字显示当前处理阶段</li>
                    <li>预计剩余时间</li>
                </ul>
            </li>
            <li><b>等待匹配完成</b>：
                <ul style="margin: 5px 0;">
                    <li>小数据集：几秒钟</li>
                    <li>中等数据集（1-5万条）：1-5分钟</li>
                    <li>大数据集（5-10万条）：5-15分钟</li>
                </ul>
            </li>
        </ol>

        <h2 style="color: #2196F3;">匹配过程监控</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>📊 实时信息显示</h3>
            <ul style="line-height: 1.8;">
                <li><b>已处理记录数</b>：当前处理进度</li>
                <li><b>已匹配数量</b>：成功匹配的记录数</li>
                <li><b>处理速度</b>：每秒处理的记录数</li>
                <li><b>预计完成时间</b>：根据当前速度估算</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">匹配完成后</h2>
        <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>📈 查看匹配统计</h3>
            <ul style="line-height: 1.8;">
                <li><b>总记录数</b>：表一和表二的记录总数</li>
                <li><b>成功匹配</b>：找到对应关系的记录数</li>
                <li><b>未匹配记录</b>：没有找到对应关系的记录</li>
                <li><b>匹配率</b>：成功匹配的百分比</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">异常情况处理</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">情况</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">处理方法</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">匹配中断</td>
                <td style="padding: 10px; border: 1px solid #ddd;">检查数据文件，重新开始匹配</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">内存不足</td>
                <td style="padding: 10px; border: 1px solid #ddd;">分批处理数据，或增加系统内存</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">匹配率过低</td>
                <td style="padding: 10px; border: 1px solid #ddd;">调整匹配参数，重新匹配</td>
            </tr>
        </table>
        """

    def _get_result_analysis_content(self):
        """结果分析内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        📊 匹配结果分析与验证
        </h1>

        <h2 style="color: #2196F3;">结果统计解读</h2>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>📈 关键指标说明</h3>
            <ul style="line-height: 1.8;">
                <li><b>匹配率</b>：成功匹配数 ÷ 总记录数 × 100%</li>
                <li><b>精确匹配</b>：所有字段完全相同的匹配</li>
                <li><b>模糊匹配</b>：通过相似度算法匹配的记录</li>
                <li><b>未匹配记录</b>：没有找到对应关系的记录</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">匹配质量评估</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">匹配率</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">质量评级</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">建议</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">≥95%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">🟢 优秀</td>
                <td style="padding: 10px; border: 1px solid #ddd;">可直接使用结果</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">85-94%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">🟡 良好</td>
                <td style="padding: 10px; border: 1px solid #ddd;">抽查验证部分结果</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">70-84%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">🟠 一般</td>
                <td style="padding: 10px; border: 1px solid #ddd;">调整参数重新匹配</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">&lt;70%</td>
                <td style="padding: 10px; border: 1px solid #ddd;">🔴 较差</td>
                <td style="padding: 10px; border: 1px solid #ddd;">检查数据质量和参数设置</td>
            </tr>
        </table>

        <h2 style="color: #2196F3;">结果验证方法</h2>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>✅ 推荐验证步骤</h3>
            <ol style="line-height: 1.8;">
                <li><b>随机抽样</b>：随机选择50-100条匹配结果</li>
                <li><b>人工核对</b>：逐一检查匹配是否正确</li>
                <li><b>重点检查</b>：关注相似度较低的匹配</li>
                <li><b>统计准确率</b>：计算人工验证的准确率</li>
            </ol>
        </div>

        <h2 style="color: #2196F3;">未匹配记录分析</h2>
        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>🔍 常见未匹配原因</h3>
            <ul style="line-height: 1.8;">
                <li><b>数据缺失</b>：某一方没有对应记录</li>
                <li><b>字段差异</b>：姓名、住院号等关键字段不一致</li>
                <li><b>日期超出容差</b>：日期差异超过设定范围</li>
                <li><b>数据错误</b>：原始数据存在录入错误</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">结果优化建议</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>🎯 提升匹配效果</h3>
            <ul style="line-height: 1.8;">
                <li><b>数据清洗</b>：统一格式，去除多余空格</li>
                <li><b>参数调优</b>：根据验证结果调整阈值</li>
                <li><b>分批处理</b>：按时间段或科室分批匹配</li>
                <li><b>人工辅助</b>：对未匹配记录进行人工处理</li>
            </ul>
        </div>
        """

    def _get_export_formats_content(self):
        """导出格式内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        📤 结果导出格式说明
        </h1>

        <h2 style="color: #2196F3;">支持的导出格式</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">格式</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">扩展名</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">适用场景</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Excel 2007+</td>
                <td style="padding: 10px; border: 1px solid #ddd;">.xlsx</td>
                <td style="padding: 10px; border: 1px solid #ddd;">推荐格式，支持多工作表</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">CSV</td>
                <td style="padding: 10px; border: 1px solid #ddd;">.csv</td>
                <td style="padding: 10px; border: 1px solid #ddd;">通用格式，便于其他系统导入</td>
            </tr>
        </table>

        <h2 style="color: #2196F3;">Excel导出内容</h2>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>📋 工作表结构</h3>
            <ul style="line-height: 1.8;">
                <li><b>匹配结果</b>：成功匹配的记录，包含两表的完整信息</li>
                <li><b>未匹配-表一</b>：表一中没有找到匹配的记录</li>
                <li><b>未匹配-表二</b>：表二中没有找到匹配的记录</li>
                <li><b>匹配统计</b>：详细的匹配统计信息</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">字段说明</h2>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>🔍 匹配结果字段</h3>
            <ul style="line-height: 1.8;">
                <li><b>表一字段</b>：姓名、住院号、手术日期、手术名称、费用等</li>
                <li><b>表二字段</b>：姓名、住院号、费用日期、费用项目、费用等</li>
                <li><b>匹配信息</b>：匹配类型、相似度、匹配时间等</li>
                <li><b>差异分析</b>：费用差额、日期差异等</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">导出操作步骤</h2>
        <ol style="line-height: 1.8; background: #f8f9fa; padding: 15px; border-radius: 8px;">
            <li>匹配完成后，点击"导出结果"按钮</li>
            <li>选择导出格式（Excel推荐）</li>
            <li>选择保存位置和文件名</li>
            <li>等待导出完成</li>
            <li>打开文件验证导出结果</li>
        </ol>

        <h2 style="color: #2196F3;">文件命名建议</h2>
        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <p><b>推荐命名格式：</b></p>
            <ul style="line-height: 1.8;">
                <li>手术费用匹配结果_YYYYMMDD.xlsx</li>
                <li>例如：手术费用匹配结果_20241210.xlsx</li>
            </ul>
        </div>
        """

    def _get_export_options_content(self):
        """导出选项内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        ⚙️ 导出选项配置
        </h1>

        <h2 style="color: #2196F3;">导出内容选择</h2>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>✅ 可选导出内容</h3>
            <ul style="line-height: 1.8;">
                <li><b>仅匹配结果</b>：只导出成功匹配的记录</li>
                <li><b>包含未匹配</b>：同时导出未匹配的记录</li>
                <li><b>完整报告</b>：包含统计信息和分析结果</li>
                <li><b>自定义字段</b>：选择需要的字段进行导出</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">字段选择</h2>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>📋 常用字段组合</h3>
            <ul style="line-height: 1.8;">
                <li><b>基础信息</b>：姓名、住院号、日期</li>
                <li><b>费用信息</b>：各类费用金额和项目</li>
                <li><b>匹配信息</b>：匹配类型、相似度</li>
                <li><b>差异分析</b>：费用差额、日期差异</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">排序选项</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>📊 排序方式</h3>
            <ul style="line-height: 1.8;">
                <li><b>按姓名</b>：字母顺序排列</li>
                <li><b>按住院号</b>：数字顺序排列</li>
                <li><b>按日期</b>：时间顺序排列</li>
                <li><b>按匹配度</b>：相似度高低排列</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">高级选项</h2>
        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>🔧 专业设置</h3>
            <ul style="line-height: 1.8;">
                <li><b>数据脱敏</b>：隐藏敏感信息</li>
                <li><b>格式化</b>：统一日期和数字格式</li>
                <li><b>添加备注</b>：在结果中添加说明信息</li>
                <li><b>分页导出</b>：大数据集分多个文件导出</li>
            </ul>
        </div>
        """

    def _get_troubleshooting_content(self):
        """故障排除内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        🔧 故障排除指南
        </h1>

        <h2 style="color: #2196F3;">程序启动问题</h2>
        <div style="background: #fff3e0; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>❌ 程序无法启动</h3>
            <ul style="line-height: 1.8;">
                <li>检查是否有杀毒软件阻止</li>
                <li>以管理员身份运行</li>
                <li>检查系统是否为64位</li>
                <li>重新下载程序文件</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">数据导入问题</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">问题</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">可能原因</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">解决方案</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">文件无法打开</td>
                <td style="padding: 10px; border: 1px solid #ddd;">文件损坏或格式不支持</td>
                <td style="padding: 10px; border: 1px solid #ddd;">检查文件格式，重新保存</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">中文显示乱码</td>
                <td style="padding: 10px; border: 1px solid #ddd;">编码格式不匹配</td>
                <td style="padding: 10px; border: 1px solid #ddd;">另存为UTF-8格式</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">导入速度很慢</td>
                <td style="padding: 10px; border: 1px solid #ddd;">文件过大或系统性能不足</td>
                <td style="padding: 10px; border: 1px solid #ddd;">分批导入或升级硬件</td>
            </tr>
        </table>

        <h2 style="color: #2196F3;">匹配问题</h2>
        <div style="background: #f0f8ff; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>🎯 匹配效果不佳</h3>
            <ul style="line-height: 1.8;">
                <li><b>检查数据质量</b>：确保关键字段完整</li>
                <li><b>调整参数</b>：降低匹配阈值</li>
                <li><b>数据预处理</b>：清理异常数据</li>
                <li><b>分批匹配</b>：按时间段处理</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">性能问题</h2>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>⚡ 程序运行缓慢</h3>
            <ul style="line-height: 1.8;">
                <li>关闭其他占用内存的程序</li>
                <li>使用SSD硬盘提升读写速度</li>
                <li>增加系统内存到8GB以上</li>
                <li>分批处理大数据集</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">联系技术支持</h2>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0;">
            <h3>📞 获取帮助</h3>
            <p>如果问题仍未解决，请联系技术支持：</p>
            <ul style="line-height: 1.8;">
                <li><b>邮箱</b>：<EMAIL></li>
                <li><b>电话</b>：400-123-4567</li>
                <li><b>QQ群</b>：123456789</li>
            </ul>
            <p><b>请提供以下信息：</b></p>
            <ul style="line-height: 1.8;">
                <li>操作系统版本</li>
                <li>程序版本号</li>
                <li>具体错误信息</li>
                <li>操作步骤</li>
            </ul>
        </div>
        """

    def _get_formats_content(self):
        """支持格式内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        📁 支持的文件格式
        </h1>

        <h2 style="color: #2196F3;">Excel 格式</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">格式</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">扩展名</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">支持程度</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Excel 2007+</td>
                <td style="padding: 10px; border: 1px solid #ddd;">.xlsx</td>
                <td style="padding: 10px; border: 1px solid #ddd;">✅ 完全支持</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">Excel 97-2003</td>
                <td style="padding: 10px; border: 1px solid #ddd;">.xls</td>
                <td style="padding: 10px; border: 1px solid #ddd;">✅ 完全支持</td>
            </tr>
        </table>

        <h2 style="color: #2196F3;">CSV 格式</h2>
        <div style="background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
            <p><b>✅ 支持多种编码</b></p>
            <ul>
                <li>UTF-8（推荐）</li>
                <li>GBK/GB2312（中文）</li>
                <li>ANSI</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">数据要求</h2>
        <h3>基本要求</h3>
        <ul style="line-height: 1.8;">
            <li>第一行必须是列标题</li>
            <li>数据从第二行开始</li>
            <li>不能有合并单元格</li>
            <li>日期格式：YYYY-MM-DD 或 YYYY/MM/DD</li>
        </ul>

        <h3>字段映射</h3>
        <p>系统支持灵活的字段映射，可以处理不同的列名：</p>
        <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0;">
            <p><b>姓名字段可识别：</b>姓名、患者姓名、病人姓名、name</p>
            <p><b>住院号字段可识别：</b>住院号、住院编号、病案号、admission_no</p>
            <p><b>日期字段可识别：</b>日期、时间、手术日期、费用日期、date</p>
        </div>
        """

    def _get_import_steps_content(self):
        """导入步骤内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        📊 数据导入详细步骤
        </h1>

        <h2 style="color: #2196F3;">步骤一：准备数据文件</h2>
        <ol style="line-height: 1.8;">
            <li>确保数据文件格式正确（Excel或CSV）</li>
            <li>检查第一行是否为列标题</li>
            <li>确认必要字段完整</li>
            <li>移除空行和无效数据</li>
        </ol>

        <h2 style="color: #2196F3;">步骤二：导入表一数据</h2>
        <ol style="line-height: 1.8;">
            <li>点击左侧面板的"导入表一"按钮</li>
            <li>在文件选择对话框中选择手术记录文件</li>
            <li>如果是Excel文件，选择正确的工作表</li>
            <li>等待数据加载完成</li>
            <li>在预览区域检查数据是否正确</li>
        </ol>

        <div style="background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0;">
            <p><b>⚠️ 注意事项</b></p>
            <ul>
                <li>大文件导入可能需要较长时间</li>
                <li>建议单次导入不超过10万条记录</li>
                <li>如果导入失败，检查文件是否被其他程序占用</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">步骤三：字段映射</h2>
        <p>系统会自动识别字段，如果识别错误，可以手动调整：</p>
        <ol style="line-height: 1.8;">
            <li>查看字段映射结果</li>
            <li>如有错误，点击"重新映射"</li>
            <li>手动选择正确的字段对应关系</li>
            <li>确认映射无误后继续</li>
        </ol>

        <h2 style="color: #2196F3;">步骤四：导入表二数据</h2>
        <p>重复表一的导入步骤，导入费用记录数据。</p>

        <h2 style="color: #2196F3;">常见导入问题</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">问题</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">解决方案</th>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">文件无法打开</td>
                <td style="padding: 10px; border: 1px solid #ddd;">检查文件格式和权限</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">中文乱码</td>
                <td style="padding: 10px; border: 1px solid #ddd;">尝试不同编码格式</td>
            </tr>
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">字段识别错误</td>
                <td style="padding: 10px; border: 1px solid #ddd;">手动重新映射字段</td>
            </tr>
        </table>
        """

    def _get_faq_content(self):
        """常见问题内容"""
        return """
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        ❓ 常见问题解答
        </h1>

        <h2 style="color: #2196F3;">导入相关问题</h2>

        <h3>Q: 为什么导入的Excel文件显示乱码？</h3>
        <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0;">
            <p><b>A:</b> 可能的原因和解决方案：</p>
            <ul>
                <li>文件编码问题：尝试将Excel另存为UTF-8格式的CSV</li>
                <li>Excel版本过旧：升级到较新版本的Excel</li>
                <li>特殊字符：检查数据中是否有特殊符号</li>
            </ul>
        </div>

        <h3>Q: 大文件导入很慢怎么办？</h3>
        <div style="background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
            <p><b>A:</b> 优化建议：</p>
            <ul>
                <li>分批导入：将大文件拆分为多个小文件</li>
                <li>关闭其他程序：释放更多内存</li>
                <li>使用SSD硬盘：提升文件读取速度</li>
                <li>升级内存：建议8GB以上</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">匹配相关问题</h2>

        <h3>Q: 匹配结果准确率不高怎么办？</h3>
        <div style="background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0;">
            <p><b>A:</b> 调整匹配参数：</p>
            <ul>
                <li>降低姓名匹配阈值（如从85%降到75%）</li>
                <li>增加日期容差天数</li>
                <li>检查数据质量，清理异常数据</li>
                <li>使用更严格的住院号匹配</li>
            </ul>
        </div>

        <h3>Q: 为什么有些明显匹配的记录没有匹配上？</h3>
        <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0;">
            <p><b>A:</b> 可能原因：</p>
            <ul>
                <li>姓名中有空格或特殊字符</li>
                <li>住院号格式不一致</li>
                <li>日期格式差异</li>
                <li>匹配阈值设置过高</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">性能相关问题</h2>

        <h3>Q: 程序启动很慢怎么办？</h3>
        <div style="background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
            <p><b>A:</b> 优化方案：</p>
            <ul>
                <li>将程序添加到杀毒软件白名单</li>
                <li>使用SSD硬盘</li>
                <li>关闭不必要的后台程序</li>
                <li>使用快速启动版本</li>
            </ul>
        </div>

        <h3>Q: 程序运行时内存占用很高？</h3>
        <div style="background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0;">
            <p><b>A:</b> 这是正常现象：</p>
            <ul>
                <li>大数据集处理需要较多内存</li>
                <li>匹配完成后内存会自动释放</li>
                <li>建议升级到8GB以上内存</li>
                <li>分批处理大文件</li>
            </ul>
        </div>

        <h2 style="color: #2196F3;">其他问题</h2>

        <h3>Q: 如何联系技术支持？</h3>
        <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0;">
            <p><b>A:</b> 联系方式：</p>
            <ul>
                <li>邮箱：<EMAIL></li>
                <li>电话：400-123-4567</li>
                <li>QQ群：123456789</li>
            </ul>
        </div>
        """

    def _get_default_content(self, topic):
        """默认内容"""
        return f"""
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        📖 {topic}
        </h1>
        
        <div style="background: #f0f8ff; padding: 20px; border-left: 4px solid #2196F3; margin: 15px 0; text-align: center;">
            <h2 style="color: #666;">内容正在完善中...</h2>
            <p>该帮助主题的详细内容正在编写中，敬请期待。</p>
            <p>如有疑问，请联系技术支持。</p>
        </div>
        
        <h2 style="color: #2196F3;">相关主题</h2>
        <ul style="line-height: 1.8;">
            <li><a href="#" style="color: #2196F3; text-decoration: none;">系统概述</a></li>
            <li><a href="#" style="color: #2196F3; text-decoration: none;">快速开始</a></li>
            <li><a href="#" style="color: #2196F3; text-decoration: none;">常见问题</a></li>
        </ul>
        """

    def _center_on_parent(self):
        """在父窗口中居中显示"""
        if self.parent():
            parent_rect = self.parent().geometry()
            x = parent_rect.x() + (parent_rect.width() - self.width()) // 2
            y = parent_rect.y() + (parent_rect.height() - self.height()) // 2
            self.move(x, y)
        else:
            # 如果没有父窗口，在屏幕中央显示
            from PySide6.QtGui import QGuiApplication
            screen = QGuiApplication.primaryScreen()
            if screen:
                screen_rect = screen.geometry()
                x = (screen_rect.width() - self.width()) // 2
                y = (screen_rect.height() - self.height()) // 2
                self.move(x, y)

    def _load_window_state(self):
        """加载窗口状态"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("SurgeryMatching", "HelpDialog")

            # 恢复窗口大小
            size = settings.value("size")
            if size:
                self.resize(size)

            # 恢复窗口位置
            pos = settings.value("pos")
            if pos:
                self.move(pos)
        except Exception:
            pass  # 如果加载失败，使用默认设置

    def _save_window_state(self):
        """保存窗口状态"""
        try:
            from PySide6.QtCore import QSettings
            settings = QSettings("SurgeryMatching", "HelpDialog")
            settings.setValue("size", self.size())
            settings.setValue("pos", self.pos())
        except Exception:
            pass  # 如果保存失败，忽略错误

    def closeEvent(self, event):
        """窗口关闭事件"""
        self._save_window_state()
        super().closeEvent(event)
