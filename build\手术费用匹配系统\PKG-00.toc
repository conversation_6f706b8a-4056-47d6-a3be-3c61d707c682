('d:\\code\\OF\\build\\手术费用匹配系统\\手术费用匹配系统.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('O', None, 'OPTION'),
  ('O', None, 'OPTION'),
  ('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'd:\\code\\OF\\build\\手术费用匹配系统\\PYZ-00.pyz', 'PYZ'),
  ('struct',
   'd:\\code\\OF\\build\\手术费用匹配系统\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'd:\\code\\OF\\build\\手术费用匹配系统\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'd:\\code\\OF\\build\\手术费用匹配系统\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'd:\\code\\OF\\build\\手术费用匹配系统\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'd:\\code\\OF\\build\\手术费用匹配系统\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'D:\\code\\OF\\venv\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main', 'D:\\code\\OF\\main.py', 'PYSOURCE-2')],
 'python310.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
