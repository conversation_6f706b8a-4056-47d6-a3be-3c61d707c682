('d:\\code\\OF\\build\\手术费用匹配系统\\PYZ-00.pyz',
 [('Levenshtein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\Levenshtein\\__init__.py',
   'PYMODULE-2'),
  ('PIL',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-2'),
  ('PIL.AvifImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BlpImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BmpImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.BufrStubImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.CurImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DcxImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.DdsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.EpsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ExifTags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-2'),
  ('PIL.FitsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FliImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FpxImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.FtexImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GbrImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GifImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.GimpGradientFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-2'),
  ('PIL.GimpPaletteFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-2'),
  ('PIL.GribStubImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcnsImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IcoImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.ImImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Image',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-2'),
  ('PIL.ImageChops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-2'),
  ('PIL.ImageCms',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-2'),
  ('PIL.ImageColor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-2'),
  ('PIL.ImageFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-2'),
  ('PIL.ImageFilter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-2'),
  ('PIL.ImageMath',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-2'),
  ('PIL.ImageMode',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-2'),
  ('PIL.ImageOps',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-2'),
  ('PIL.ImagePalette',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-2'),
  ('PIL.ImageQt',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-2'),
  ('PIL.ImageSequence',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-2'),
  ('PIL.ImageShow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-2'),
  ('PIL.ImageTk',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-2'),
  ('PIL.ImageWin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-2'),
  ('PIL.ImtImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.IptcImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.JpegPresets',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-2'),
  ('PIL.McIdasImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MicImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpegImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MpoImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.MspImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PaletteFile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-2'),
  ('PIL.PalmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcdImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PcxImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PdfParser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-2'),
  ('PIL.PixarImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PngImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PpmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.PsdImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.QoiImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SgiImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SpiderImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.SunImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TgaImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.TiffTags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-2'),
  ('PIL.WebPImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.WmfImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XVThumbImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XbmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL.XpmImagePlugin',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-2'),
  ('PIL._binary',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-2'),
  ('PIL._deprecate',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-2'),
  ('PIL._typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-2'),
  ('PIL._util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-2'),
  ('PIL._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-2'),
  ('PIL.features',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PIL\\features.py',
   'PYMODULE-2'),
  ('PySide6',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\__init__.py',
   'PYMODULE-2'),
  ('PySide6.support',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE-2'),
  ('PySide6.support.deprecated',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE-2'),
  ('__future__', 'D:\\Python\\Python310\\lib\\__future__.py', 'PYMODULE-2'),
  ('_aix_support', 'D:\\Python\\Python310\\lib\\_aix_support.py', 'PYMODULE-2'),
  ('_bootsubprocess',
   'D:\\Python\\Python310\\lib\\_bootsubprocess.py',
   'PYMODULE-2'),
  ('_compat_pickle',
   'D:\\Python\\Python310\\lib\\_compat_pickle.py',
   'PYMODULE-2'),
  ('_compression', 'D:\\Python\\Python310\\lib\\_compression.py', 'PYMODULE-2'),
  ('_py_abc', 'D:\\Python\\Python310\\lib\\_py_abc.py', 'PYMODULE-2'),
  ('_pydecimal', 'D:\\Python\\Python310\\lib\\_pydecimal.py', 'PYMODULE-2'),
  ('_pyi_rth_utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE-2'),
  ('_pyi_rth_utils.qt',
   'D:\\code\\OF\\venv\\lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE-2'),
  ('_strptime', 'D:\\Python\\Python310\\lib\\_strptime.py', 'PYMODULE-2'),
  ('_threading_local',
   'D:\\Python\\Python310\\lib\\_threading_local.py',
   'PYMODULE-2'),
  ('argparse', 'D:\\Python\\Python310\\lib\\argparse.py', 'PYMODULE-2'),
  ('ast', 'D:\\Python\\Python310\\lib\\ast.py', 'PYMODULE-2'),
  ('asyncio', 'D:\\Python\\Python310\\lib\\asyncio\\__init__.py', 'PYMODULE-2'),
  ('asyncio.base_events',
   'D:\\Python\\Python310\\lib\\asyncio\\base_events.py',
   'PYMODULE-2'),
  ('asyncio.base_futures',
   'D:\\Python\\Python310\\lib\\asyncio\\base_futures.py',
   'PYMODULE-2'),
  ('asyncio.base_subprocess',
   'D:\\Python\\Python310\\lib\\asyncio\\base_subprocess.py',
   'PYMODULE-2'),
  ('asyncio.base_tasks',
   'D:\\Python\\Python310\\lib\\asyncio\\base_tasks.py',
   'PYMODULE-2'),
  ('asyncio.constants',
   'D:\\Python\\Python310\\lib\\asyncio\\constants.py',
   'PYMODULE-2'),
  ('asyncio.coroutines',
   'D:\\Python\\Python310\\lib\\asyncio\\coroutines.py',
   'PYMODULE-2'),
  ('asyncio.events',
   'D:\\Python\\Python310\\lib\\asyncio\\events.py',
   'PYMODULE-2'),
  ('asyncio.exceptions',
   'D:\\Python\\Python310\\lib\\asyncio\\exceptions.py',
   'PYMODULE-2'),
  ('asyncio.format_helpers',
   'D:\\Python\\Python310\\lib\\asyncio\\format_helpers.py',
   'PYMODULE-2'),
  ('asyncio.futures',
   'D:\\Python\\Python310\\lib\\asyncio\\futures.py',
   'PYMODULE-2'),
  ('asyncio.locks',
   'D:\\Python\\Python310\\lib\\asyncio\\locks.py',
   'PYMODULE-2'),
  ('asyncio.log', 'D:\\Python\\Python310\\lib\\asyncio\\log.py', 'PYMODULE-2'),
  ('asyncio.mixins',
   'D:\\Python\\Python310\\lib\\asyncio\\mixins.py',
   'PYMODULE-2'),
  ('asyncio.proactor_events',
   'D:\\Python\\Python310\\lib\\asyncio\\proactor_events.py',
   'PYMODULE-2'),
  ('asyncio.protocols',
   'D:\\Python\\Python310\\lib\\asyncio\\protocols.py',
   'PYMODULE-2'),
  ('asyncio.queues',
   'D:\\Python\\Python310\\lib\\asyncio\\queues.py',
   'PYMODULE-2'),
  ('asyncio.runners',
   'D:\\Python\\Python310\\lib\\asyncio\\runners.py',
   'PYMODULE-2'),
  ('asyncio.selector_events',
   'D:\\Python\\Python310\\lib\\asyncio\\selector_events.py',
   'PYMODULE-2'),
  ('asyncio.sslproto',
   'D:\\Python\\Python310\\lib\\asyncio\\sslproto.py',
   'PYMODULE-2'),
  ('asyncio.staggered',
   'D:\\Python\\Python310\\lib\\asyncio\\staggered.py',
   'PYMODULE-2'),
  ('asyncio.streams',
   'D:\\Python\\Python310\\lib\\asyncio\\streams.py',
   'PYMODULE-2'),
  ('asyncio.subprocess',
   'D:\\Python\\Python310\\lib\\asyncio\\subprocess.py',
   'PYMODULE-2'),
  ('asyncio.tasks',
   'D:\\Python\\Python310\\lib\\asyncio\\tasks.py',
   'PYMODULE-2'),
  ('asyncio.threads',
   'D:\\Python\\Python310\\lib\\asyncio\\threads.py',
   'PYMODULE-2'),
  ('asyncio.transports',
   'D:\\Python\\Python310\\lib\\asyncio\\transports.py',
   'PYMODULE-2'),
  ('asyncio.trsock',
   'D:\\Python\\Python310\\lib\\asyncio\\trsock.py',
   'PYMODULE-2'),
  ('asyncio.unix_events',
   'D:\\Python\\Python310\\lib\\asyncio\\unix_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_events',
   'D:\\Python\\Python310\\lib\\asyncio\\windows_events.py',
   'PYMODULE-2'),
  ('asyncio.windows_utils',
   'D:\\Python\\Python310\\lib\\asyncio\\windows_utils.py',
   'PYMODULE-2'),
  ('base64', 'D:\\Python\\Python310\\lib\\base64.py', 'PYMODULE-2'),
  ('bdb', 'D:\\Python\\Python310\\lib\\bdb.py', 'PYMODULE-2'),
  ('bisect', 'D:\\Python\\Python310\\lib\\bisect.py', 'PYMODULE-2'),
  ('bz2', 'D:\\Python\\Python310\\lib\\bz2.py', 'PYMODULE-2'),
  ('calendar', 'D:\\Python\\Python310\\lib\\calendar.py', 'PYMODULE-2'),
  ('cmd', 'D:\\Python\\Python310\\lib\\cmd.py', 'PYMODULE-2'),
  ('code', 'D:\\Python\\Python310\\lib\\code.py', 'PYMODULE-2'),
  ('codeop', 'D:\\Python\\Python310\\lib\\codeop.py', 'PYMODULE-2'),
  ('colorsys', 'D:\\Python\\Python310\\lib\\colorsys.py', 'PYMODULE-2'),
  ('concurrent',
   'D:\\Python\\Python310\\lib\\concurrent\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-2'),
  ('concurrent.futures._base',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\_base.py',
   'PYMODULE-2'),
  ('concurrent.futures.process',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\process.py',
   'PYMODULE-2'),
  ('concurrent.futures.thread',
   'D:\\Python\\Python310\\lib\\concurrent\\futures\\thread.py',
   'PYMODULE-2'),
  ('contextlib', 'D:\\Python\\Python310\\lib\\contextlib.py', 'PYMODULE-2'),
  ('contextvars', 'D:\\Python\\Python310\\lib\\contextvars.py', 'PYMODULE-2'),
  ('copy', 'D:\\Python\\Python310\\lib\\copy.py', 'PYMODULE-2'),
  ('csv', 'D:\\Python\\Python310\\lib\\csv.py', 'PYMODULE-2'),
  ('ctypes', 'D:\\Python\\Python310\\lib\\ctypes\\__init__.py', 'PYMODULE-2'),
  ('ctypes._endian',
   'D:\\Python\\Python310\\lib\\ctypes\\_endian.py',
   'PYMODULE-2'),
  ('ctypes.wintypes',
   'D:\\Python\\Python310\\lib\\ctypes\\wintypes.py',
   'PYMODULE-2'),
  ('dataclasses', 'D:\\Python\\Python310\\lib\\dataclasses.py', 'PYMODULE-2'),
  ('datetime', 'D:\\Python\\Python310\\lib\\datetime.py', 'PYMODULE-2'),
  ('dateutil',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-2'),
  ('dateutil._common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-2'),
  ('dateutil._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-2'),
  ('dateutil.easter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-2'),
  ('dateutil.parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.parser._parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-2'),
  ('dateutil.parser.isoparser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-2'),
  ('dateutil.relativedelta',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-2'),
  ('dateutil.rrule',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-2'),
  ('dateutil.tz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-2'),
  ('dateutil.tz._common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-2'),
  ('dateutil.tz._factories',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-2'),
  ('dateutil.tz.tz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-2'),
  ('dateutil.tz.win',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-2'),
  ('dateutil.zoneinfo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-2'),
  ('decimal', 'D:\\Python\\Python310\\lib\\decimal.py', 'PYMODULE-2'),
  ('difflib', 'D:\\Python\\Python310\\lib\\difflib.py', 'PYMODULE-2'),
  ('dis', 'D:\\Python\\Python310\\lib\\dis.py', 'PYMODULE-2'),
  ('doctest', 'D:\\Python\\Python310\\lib\\doctest.py', 'PYMODULE-2'),
  ('email', 'D:\\Python\\Python310\\lib\\email\\__init__.py', 'PYMODULE-2'),
  ('email._encoded_words',
   'D:\\Python\\Python310\\lib\\email\\_encoded_words.py',
   'PYMODULE-2'),
  ('email._header_value_parser',
   'D:\\Python\\Python310\\lib\\email\\_header_value_parser.py',
   'PYMODULE-2'),
  ('email._parseaddr',
   'D:\\Python\\Python310\\lib\\email\\_parseaddr.py',
   'PYMODULE-2'),
  ('email._policybase',
   'D:\\Python\\Python310\\lib\\email\\_policybase.py',
   'PYMODULE-2'),
  ('email.base64mime',
   'D:\\Python\\Python310\\lib\\email\\base64mime.py',
   'PYMODULE-2'),
  ('email.charset',
   'D:\\Python\\Python310\\lib\\email\\charset.py',
   'PYMODULE-2'),
  ('email.contentmanager',
   'D:\\Python\\Python310\\lib\\email\\contentmanager.py',
   'PYMODULE-2'),
  ('email.encoders',
   'D:\\Python\\Python310\\lib\\email\\encoders.py',
   'PYMODULE-2'),
  ('email.errors',
   'D:\\Python\\Python310\\lib\\email\\errors.py',
   'PYMODULE-2'),
  ('email.feedparser',
   'D:\\Python\\Python310\\lib\\email\\feedparser.py',
   'PYMODULE-2'),
  ('email.generator',
   'D:\\Python\\Python310\\lib\\email\\generator.py',
   'PYMODULE-2'),
  ('email.header',
   'D:\\Python\\Python310\\lib\\email\\header.py',
   'PYMODULE-2'),
  ('email.headerregistry',
   'D:\\Python\\Python310\\lib\\email\\headerregistry.py',
   'PYMODULE-2'),
  ('email.iterators',
   'D:\\Python\\Python310\\lib\\email\\iterators.py',
   'PYMODULE-2'),
  ('email.message',
   'D:\\Python\\Python310\\lib\\email\\message.py',
   'PYMODULE-2'),
  ('email.parser',
   'D:\\Python\\Python310\\lib\\email\\parser.py',
   'PYMODULE-2'),
  ('email.policy',
   'D:\\Python\\Python310\\lib\\email\\policy.py',
   'PYMODULE-2'),
  ('email.quoprimime',
   'D:\\Python\\Python310\\lib\\email\\quoprimime.py',
   'PYMODULE-2'),
  ('email.utils', 'D:\\Python\\Python310\\lib\\email\\utils.py', 'PYMODULE-2'),
  ('et_xmlfile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE-2'),
  ('et_xmlfile.incremental_tree',
   'D:\\code\\OF\\venv\\lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE-2'),
  ('et_xmlfile.xmlfile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE-2'),
  ('fileinput', 'D:\\Python\\Python310\\lib\\fileinput.py', 'PYMODULE-2'),
  ('fnmatch', 'D:\\Python\\Python310\\lib\\fnmatch.py', 'PYMODULE-2'),
  ('fractions', 'D:\\Python\\Python310\\lib\\fractions.py', 'PYMODULE-2'),
  ('ftplib', 'D:\\Python\\Python310\\lib\\ftplib.py', 'PYMODULE-2'),
  ('fuzzywuzzy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\__init__.py',
   'PYMODULE-2'),
  ('fuzzywuzzy.StringMatcher',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\StringMatcher.py',
   'PYMODULE-2'),
  ('fuzzywuzzy.fuzz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\fuzz.py',
   'PYMODULE-2'),
  ('fuzzywuzzy.string_processing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\string_processing.py',
   'PYMODULE-2'),
  ('fuzzywuzzy.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\fuzzywuzzy\\utils.py',
   'PYMODULE-2'),
  ('getopt', 'D:\\Python\\Python310\\lib\\getopt.py', 'PYMODULE-2'),
  ('getpass', 'D:\\Python\\Python310\\lib\\getpass.py', 'PYMODULE-2'),
  ('gettext', 'D:\\Python\\Python310\\lib\\gettext.py', 'PYMODULE-2'),
  ('glob', 'D:\\Python\\Python310\\lib\\glob.py', 'PYMODULE-2'),
  ('gzip', 'D:\\Python\\Python310\\lib\\gzip.py', 'PYMODULE-2'),
  ('hashlib', 'D:\\Python\\Python310\\lib\\hashlib.py', 'PYMODULE-2'),
  ('hmac', 'D:\\Python\\Python310\\lib\\hmac.py', 'PYMODULE-2'),
  ('html', 'D:\\Python\\Python310\\lib\\html\\__init__.py', 'PYMODULE-2'),
  ('html.entities',
   'D:\\Python\\Python310\\lib\\html\\entities.py',
   'PYMODULE-2'),
  ('http', 'D:\\Python\\Python310\\lib\\http\\__init__.py', 'PYMODULE-2'),
  ('http.client', 'D:\\Python\\Python310\\lib\\http\\client.py', 'PYMODULE-2'),
  ('http.cookiejar',
   'D:\\Python\\Python310\\lib\\http\\cookiejar.py',
   'PYMODULE-2'),
  ('http.server', 'D:\\Python\\Python310\\lib\\http\\server.py', 'PYMODULE-2'),
  ('importlib',
   'D:\\Python\\Python310\\lib\\importlib\\__init__.py',
   'PYMODULE-2'),
  ('importlib._abc',
   'D:\\Python\\Python310\\lib\\importlib\\_abc.py',
   'PYMODULE-2'),
  ('importlib._bootstrap',
   'D:\\Python\\Python310\\lib\\importlib\\_bootstrap.py',
   'PYMODULE-2'),
  ('importlib._bootstrap_external',
   'D:\\Python\\Python310\\lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-2'),
  ('importlib.abc',
   'D:\\Python\\Python310\\lib\\importlib\\abc.py',
   'PYMODULE-2'),
  ('importlib.machinery',
   'D:\\Python\\Python310\\lib\\importlib\\machinery.py',
   'PYMODULE-2'),
  ('importlib.metadata',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-2'),
  ('importlib.metadata._adapters',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-2'),
  ('importlib.metadata._collections',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-2'),
  ('importlib.metadata._functools',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-2'),
  ('importlib.metadata._itertools',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-2'),
  ('importlib.metadata._meta',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-2'),
  ('importlib.metadata._text',
   'D:\\Python\\Python310\\lib\\importlib\\metadata\\_text.py',
   'PYMODULE-2'),
  ('importlib.readers',
   'D:\\Python\\Python310\\lib\\importlib\\readers.py',
   'PYMODULE-2'),
  ('importlib.util',
   'D:\\Python\\Python310\\lib\\importlib\\util.py',
   'PYMODULE-2'),
  ('inspect', 'D:\\Python\\Python310\\lib\\inspect.py', 'PYMODULE-2'),
  ('json', 'D:\\Python\\Python310\\lib\\json\\__init__.py', 'PYMODULE-2'),
  ('json.decoder',
   'D:\\Python\\Python310\\lib\\json\\decoder.py',
   'PYMODULE-2'),
  ('json.encoder',
   'D:\\Python\\Python310\\lib\\json\\encoder.py',
   'PYMODULE-2'),
  ('json.scanner',
   'D:\\Python\\Python310\\lib\\json\\scanner.py',
   'PYMODULE-2'),
  ('logging', 'D:\\Python\\Python310\\lib\\logging\\__init__.py', 'PYMODULE-2'),
  ('logging.handlers',
   'D:\\Python\\Python310\\lib\\logging\\handlers.py',
   'PYMODULE-2'),
  ('lzma', 'D:\\Python\\Python310\\lib\\lzma.py', 'PYMODULE-2'),
  ('mimetypes', 'D:\\Python\\Python310\\lib\\mimetypes.py', 'PYMODULE-2'),
  ('multiprocessing',
   'D:\\Python\\Python310\\lib\\multiprocessing\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.connection',
   'D:\\Python\\Python310\\lib\\multiprocessing\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.context',
   'D:\\Python\\Python310\\lib\\multiprocessing\\context.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy',
   'D:\\Python\\Python310\\lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-2'),
  ('multiprocessing.dummy.connection',
   'D:\\Python\\Python310\\lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-2'),
  ('multiprocessing.forkserver',
   'D:\\Python\\Python310\\lib\\multiprocessing\\forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.heap',
   'D:\\Python\\Python310\\lib\\multiprocessing\\heap.py',
   'PYMODULE-2'),
  ('multiprocessing.managers',
   'D:\\Python\\Python310\\lib\\multiprocessing\\managers.py',
   'PYMODULE-2'),
  ('multiprocessing.pool',
   'D:\\Python\\Python310\\lib\\multiprocessing\\pool.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_fork',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_forkserver',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-2'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Python\\Python310\\lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-2'),
  ('multiprocessing.process',
   'D:\\Python\\Python310\\lib\\multiprocessing\\process.py',
   'PYMODULE-2'),
  ('multiprocessing.queues',
   'D:\\Python\\Python310\\lib\\multiprocessing\\queues.py',
   'PYMODULE-2'),
  ('multiprocessing.reduction',
   'D:\\Python\\Python310\\lib\\multiprocessing\\reduction.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_sharer',
   'D:\\Python\\Python310\\lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-2'),
  ('multiprocessing.resource_tracker',
   'D:\\Python\\Python310\\lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-2'),
  ('multiprocessing.shared_memory',
   'D:\\Python\\Python310\\lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-2'),
  ('multiprocessing.sharedctypes',
   'D:\\Python\\Python310\\lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-2'),
  ('multiprocessing.spawn',
   'D:\\Python\\Python310\\lib\\multiprocessing\\spawn.py',
   'PYMODULE-2'),
  ('multiprocessing.synchronize',
   'D:\\Python\\Python310\\lib\\multiprocessing\\synchronize.py',
   'PYMODULE-2'),
  ('multiprocessing.util',
   'D:\\Python\\Python310\\lib\\multiprocessing\\util.py',
   'PYMODULE-2'),
  ('netrc', 'D:\\Python\\Python310\\lib\\netrc.py', 'PYMODULE-2'),
  ('nturl2path', 'D:\\Python\\Python310\\lib\\nturl2path.py', 'PYMODULE-2'),
  ('numbers', 'D:\\Python\\Python310\\lib\\numbers.py', 'PYMODULE-2'),
  ('numpy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('numpy.__config__',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-2'),
  ('numpy._array_api_info',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-2'),
  ('numpy._core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-2'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-2'),
  ('numpy._core._asarray',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-2'),
  ('numpy._core._dtype',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-2'),
  ('numpy._core._dtype_ctypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-2'),
  ('numpy._core._exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_exceptions.py',
   'PYMODULE-2'),
  ('numpy._core._internal',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-2'),
  ('numpy._core._machar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-2'),
  ('numpy._core._methods',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-2'),
  ('numpy._core._string_helpers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-2'),
  ('numpy._core._type_aliases',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-2'),
  ('numpy._core._ufunc_config',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-2'),
  ('numpy._core.arrayprint',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-2'),
  ('numpy._core.defchararray',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-2'),
  ('numpy._core.einsumfunc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-2'),
  ('numpy._core.fromnumeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-2'),
  ('numpy._core.function_base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-2'),
  ('numpy._core.getlimits',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-2'),
  ('numpy._core.memmap',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-2'),
  ('numpy._core.multiarray',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-2'),
  ('numpy._core.numeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-2'),
  ('numpy._core.numerictypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-2'),
  ('numpy._core.overrides',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-2'),
  ('numpy._core.printoptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-2'),
  ('numpy._core.records',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-2'),
  ('numpy._core.shape_base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-2'),
  ('numpy._core.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-2'),
  ('numpy._core.umath',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-2'),
  ('numpy._distributor_init',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-2'),
  ('numpy._expired_attrs_2_0',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-2'),
  ('numpy._globals',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-2'),
  ('numpy._pytesttester',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-2'),
  ('numpy._typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy._typing._add_docstring',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-2'),
  ('numpy._typing._array_like',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-2'),
  ('numpy._typing._char_codes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-2'),
  ('numpy._typing._dtype_like',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-2'),
  ('numpy._typing._nbit_base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-2'),
  ('numpy._typing._nested_sequence',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-2'),
  ('numpy._typing._scalars',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-2'),
  ('numpy._typing._shape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-2'),
  ('numpy._typing._ufunc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-2'),
  ('numpy._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-2'),
  ('numpy._utils._convertions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-2'),
  ('numpy._utils._inspect',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-2'),
  ('numpy.char',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-2'),
  ('numpy.core._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-2'),
  ('numpy.ctypeslib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE-2'),
  ('numpy.dtypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-2'),
  ('numpy.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-2'),
  ('numpy.f2py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py.__version__',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._backend',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._distutils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-2'),
  ('numpy.f2py._backends._meson',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-2'),
  ('numpy.f2py._isocbind',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-2'),
  ('numpy.f2py.auxfuncs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.capi_maps',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-2'),
  ('numpy.f2py.cb_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.cfuncs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-2'),
  ('numpy.f2py.common_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.crackfortran',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-2'),
  ('numpy.f2py.diagnose',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-2'),
  ('numpy.f2py.f2py2e',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-2'),
  ('numpy.f2py.f90mod_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.func2subr',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-2'),
  ('numpy.f2py.rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-2'),
  ('numpy.f2py.symbolic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-2'),
  ('numpy.f2py.use_rules',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-2'),
  ('numpy.fft',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-2'),
  ('numpy.fft._helper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-2'),
  ('numpy.fft._pocketfft',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-2'),
  ('numpy.fft.helper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-2'),
  ('numpy.lib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.lib._array_utils_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraypad_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arraysetops_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._arrayterator_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._datasource',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-2'),
  ('numpy.lib._function_base_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._histograms_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._index_tricks_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._iotools',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-2'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._npyio_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._polynomial_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._scimath_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._shape_base_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._twodim_base_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._type_check_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._ufunclike_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._utils_impl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-2'),
  ('numpy.lib._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-2'),
  ('numpy.lib.array_utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-2'),
  ('numpy.lib.format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-2'),
  ('numpy.lib.introspect',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-2'),
  ('numpy.lib.mixins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-2'),
  ('numpy.lib.npyio',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-2'),
  ('numpy.lib.scimath',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-2'),
  ('numpy.lib.stride_tricks',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-2'),
  ('numpy.linalg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-2'),
  ('numpy.linalg._linalg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-2'),
  ('numpy.linalg.linalg',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-2'),
  ('numpy.ma',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-2'),
  ('numpy.ma.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-2'),
  ('numpy.ma.extras',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-2'),
  ('numpy.ma.mrecords',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-2'),
  ('numpy.matlib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-2'),
  ('numpy.matrixlib',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-2'),
  ('numpy.matrixlib.defmatrix',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-2'),
  ('numpy.polynomial',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-2'),
  ('numpy.polynomial._polybase',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-2'),
  ('numpy.polynomial.chebyshev',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-2'),
  ('numpy.polynomial.hermite_e',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-2'),
  ('numpy.polynomial.laguerre',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.legendre',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polynomial',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-2'),
  ('numpy.polynomial.polyutils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-2'),
  ('numpy.random',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-2'),
  ('numpy.random._pickle',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-2'),
  ('numpy.rec',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-2'),
  ('numpy.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-2'),
  ('numpy.typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-2'),
  ('numpy.version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\numpy\\version.py',
   'PYMODULE-2'),
  ('opcode', 'D:\\Python\\Python310\\lib\\opcode.py', 'PYMODULE-2'),
  ('openpyxl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl._constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE-2'),
  ('openpyxl.cell',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.cell._writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.cell.cell',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.cell.read_only',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE-2'),
  ('openpyxl.cell.rich_text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE-2'),
  ('openpyxl.cell.text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE-2'),
  ('openpyxl.chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chart._3d',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE-2'),
  ('openpyxl.chart._chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.area_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.axis',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bar_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.bubble_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.chartspace',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE-2'),
  ('openpyxl.chart.data_source',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE-2'),
  ('openpyxl.chart.descriptors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE-2'),
  ('openpyxl.chart.error_bar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE-2'),
  ('openpyxl.chart.label',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE-2'),
  ('openpyxl.chart.layout',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE-2'),
  ('openpyxl.chart.legend',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE-2'),
  ('openpyxl.chart.line_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.marker',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE-2'),
  ('openpyxl.chart.picture',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pie_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.pivot',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE-2'),
  ('openpyxl.chart.plotarea',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE-2'),
  ('openpyxl.chart.print_settings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.chart.radar_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE-2'),
  ('openpyxl.chart.reference',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE-2'),
  ('openpyxl.chart.scatter_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE-2'),
  ('openpyxl.chart.series_factory',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE-2'),
  ('openpyxl.chart.shapes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE-2'),
  ('openpyxl.chart.stock_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.surface_chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE-2'),
  ('openpyxl.chart.text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE-2'),
  ('openpyxl.chart.title',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE-2'),
  ('openpyxl.chart.trendline',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE-2'),
  ('openpyxl.chart.updown_bars',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.custom',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.publish',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.relation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.chartsheet.views',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.comments',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.comments.author',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comment_sheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE-2'),
  ('openpyxl.comments.comments',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE-2'),
  ('openpyxl.comments.shape_writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE-2'),
  ('openpyxl.compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.compat.numbers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.compat.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.container',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.namespace',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.nested',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.sequence',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE-2'),
  ('openpyxl.descriptors.serialisable',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE-2'),
  ('openpyxl.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.colors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.connector',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.effect',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.fill',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.geometry',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.graphic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.image',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.line',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.picture',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.relation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.text',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE-2'),
  ('openpyxl.drawing.xdr',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE-2'),
  ('openpyxl.formatting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.formatting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE-2'),
  ('openpyxl.formatting.rule',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE-2'),
  ('openpyxl.formula',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.formula.tokenizer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE-2'),
  ('openpyxl.formula.translate',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE-2'),
  ('openpyxl.packaging',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.custom',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.extended',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.manifest',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.relationship',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE-2'),
  ('openpyxl.packaging.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.pivot',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.cache',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.fields',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.record',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE-2'),
  ('openpyxl.pivot.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE-2'),
  ('openpyxl.reader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.reader.drawings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE-2'),
  ('openpyxl.reader.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.reader.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE-2'),
  ('openpyxl.reader.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.styles',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.styles.alignment',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE-2'),
  ('openpyxl.styles.borders',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE-2'),
  ('openpyxl.styles.builtins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE-2'),
  ('openpyxl.styles.cell_style',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE-2'),
  ('openpyxl.styles.colors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE-2'),
  ('openpyxl.styles.differential',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fills',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE-2'),
  ('openpyxl.styles.fonts',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE-2'),
  ('openpyxl.styles.named_styles',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE-2'),
  ('openpyxl.styles.numbers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE-2'),
  ('openpyxl.styles.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.styles.proxy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE-2'),
  ('openpyxl.styles.styleable',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE-2'),
  ('openpyxl.styles.stylesheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE-2'),
  ('openpyxl.styles.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE-2'),
  ('openpyxl.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE-2'),
  ('openpyxl.utils.cell',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE-2'),
  ('openpyxl.utils.datetime',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE-2'),
  ('openpyxl.utils.escape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE-2'),
  ('openpyxl.utils.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE-2'),
  ('openpyxl.utils.formulas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE-2'),
  ('openpyxl.utils.indexed_list',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE-2'),
  ('openpyxl.utils.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.utils.units',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE-2'),
  ('openpyxl.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook._writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.child',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.defined_name',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_link.external',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.external_reference',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.function_group',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.smart_tags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.views',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.web',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE-2'),
  ('openpyxl.workbook.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._read_only',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._reader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._write_only',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet._writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.cell_range',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.copier',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.dimensions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.filters',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.formula',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.header_footer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.merge',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.page',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.print_settings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.properties',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.protection',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.related',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.scenario',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.views',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE-2'),
  ('openpyxl.worksheet.worksheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE-2'),
  ('openpyxl.writer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.writer.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE-2'),
  ('openpyxl.writer.theme',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE-2'),
  ('openpyxl.xml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE-2'),
  ('openpyxl.xml.constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE-2'),
  ('openpyxl.xml.functions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE-2'),
  ('optparse', 'D:\\Python\\Python310\\lib\\optparse.py', 'PYMODULE-2'),
  ('pandas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-2'),
  ('pandas._config.config',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-2'),
  ('pandas._config.dates',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-2'),
  ('pandas._config.display',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-2'),
  ('pandas._config.localization',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-2'),
  ('pandas._libs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.tslibs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-2'),
  ('pandas._libs.window',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-2'),
  ('pandas._testing._io',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-2'),
  ('pandas._testing._warnings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-2'),
  ('pandas._testing.asserters',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-2'),
  ('pandas._testing.compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-2'),
  ('pandas._testing.contexts',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-2'),
  ('pandas._typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-2'),
  ('pandas._version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-2'),
  ('pandas._version_meson',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE-2'),
  ('pandas.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.extensions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.indexers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.interchange',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.types',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-2'),
  ('pandas.api.typing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE-2'),
  ('pandas.arrays',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat._constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-2'),
  ('pandas.compat._optional',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-2'),
  ('pandas.compat.compressors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-2'),
  ('pandas.compat.numpy.function',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-2'),
  ('pandas.compat.pickle_compat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-2'),
  ('pandas.compat.pyarrow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-2'),
  ('pandas.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.executor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-2'),
  ('pandas.core._numba.extensions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.shared',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-2'),
  ('pandas.core._numba.kernels.var_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-2'),
  ('pandas.core.accessor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.algorithms',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-2'),
  ('pandas.core.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-2'),
  ('pandas.core.apply',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.putmask',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.quantile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.replace',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.take',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-2'),
  ('pandas.core.array_algos.transforms',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-2'),
  ('pandas.core.arraylike',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-2'),
  ('pandas.core.arrays',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._mixins',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._ranges',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-2'),
  ('pandas.core.arrays._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.array',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.boolean',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.categorical',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimelike',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.datetimes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.floating',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.integer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.interval',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.masked',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.numpy_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.period',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.array',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.string_arrow',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-2'),
  ('pandas.core.arrays.timedeltas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-2'),
  ('pandas.core.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-2'),
  ('pandas.core.computation',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.computation.align',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-2'),
  ('pandas.core.computation.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-2'),
  ('pandas.core.computation.check',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-2'),
  ('pandas.core.computation.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-2'),
  ('pandas.core.computation.engines',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-2'),
  ('pandas.core.computation.eval',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expr',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-2'),
  ('pandas.core.computation.expressions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-2'),
  ('pandas.core.computation.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.computation.parsing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-2'),
  ('pandas.core.computation.pytables',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-2'),
  ('pandas.core.computation.scope',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-2'),
  ('pandas.core.config_init',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-2'),
  ('pandas.core.construction',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.astype',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.cast',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.concat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.dtypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.generic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.inference',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-2'),
  ('pandas.core.dtypes.missing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.flags',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-2'),
  ('pandas.core.frame',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-2'),
  ('pandas.core.generic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.groupby',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.categorical',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.generic',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.groupby',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.grouper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.indexing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.numba_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.groupby.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.indexers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.objects',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-2'),
  ('pandas.core.indexers.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.indexes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.accessors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.category',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimelike',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.datetimes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.extension',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.frozen',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.interval',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.multi',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.period',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.range',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-2'),
  ('pandas.core.indexes.timedeltas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.indexing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-2'),
  ('pandas.core.interchange',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.buffer',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.column',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-2'),
  ('pandas.core.interchange.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-2'),
  ('pandas.core.internals',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.internals.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-2'),
  ('pandas.core.internals.array_manager',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-2'),
  ('pandas.core.internals.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-2'),
  ('pandas.core.internals.blocks',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-2'),
  ('pandas.core.internals.concat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.internals.construction',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-2'),
  ('pandas.core.internals.managers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-2'),
  ('pandas.core.internals.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-2'),
  ('pandas.core.methods',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.methods.describe',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-2'),
  ('pandas.core.methods.selectn',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-2'),
  ('pandas.core.methods.to_dict',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-2'),
  ('pandas.core.missing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.nanops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-2'),
  ('pandas.core.ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.ops.array_ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-2'),
  ('pandas.core.ops.dispatch',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-2'),
  ('pandas.core.ops.docstrings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-2'),
  ('pandas.core.ops.invalid',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-2'),
  ('pandas.core.ops.mask_ops',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-2'),
  ('pandas.core.ops.missing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-2'),
  ('pandas.core.resample',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-2'),
  ('pandas.core.reshape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.concat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.encoding',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.melt',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.merge',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.pivot',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.reshape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.tile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-2'),
  ('pandas.core.reshape.util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-2'),
  ('pandas.core.roperator',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-2'),
  ('pandas.core.sample',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-2'),
  ('pandas.core.series',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-2'),
  ('pandas.core.shared_docs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-2'),
  ('pandas.core.sorting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-2'),
  ('pandas.core.strings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.strings.accessor',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-2'),
  ('pandas.core.strings.base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-2'),
  ('pandas.core.strings.object_array',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-2'),
  ('pandas.core.tools',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.tools.datetimes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-2'),
  ('pandas.core.tools.numeric',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-2'),
  ('pandas.core.tools.timedeltas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-2'),
  ('pandas.core.tools.times',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-2'),
  ('pandas.core.util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.util.hashing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-2'),
  ('pandas.core.util.numba_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-2'),
  ('pandas.core.window.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-2'),
  ('pandas.core.window.doc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-2'),
  ('pandas.core.window.ewm',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-2'),
  ('pandas.core.window.expanding',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-2'),
  ('pandas.core.window.numba_',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-2'),
  ('pandas.core.window.online',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-2'),
  ('pandas.core.window.rolling',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-2'),
  ('pandas.errors',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io._util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-2'),
  ('pandas.io.clipboard',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.clipboards',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-2'),
  ('pandas.io.common',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-2'),
  ('pandas.io.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.excel._base',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-2'),
  ('pandas.io.excel._calamine',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odfreader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-2'),
  ('pandas.io.excel._odswriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-2'),
  ('pandas.io.excel._openpyxl',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-2'),
  ('pandas.io.excel._pyxlsb',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-2'),
  ('pandas.io.excel._util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlrd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-2'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-2'),
  ('pandas.io.feather_format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-2'),
  ('pandas.io.formats',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.formats._color_data',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-2'),
  ('pandas.io.formats.console',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-2'),
  ('pandas.io.formats.css',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-2'),
  ('pandas.io.formats.csvs',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-2'),
  ('pandas.io.formats.excel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-2'),
  ('pandas.io.formats.format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-2'),
  ('pandas.io.formats.html',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-2'),
  ('pandas.io.formats.info',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-2'),
  ('pandas.io.formats.printing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-2'),
  ('pandas.io.formats.string',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-2'),
  ('pandas.io.formats.style_render',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-2'),
  ('pandas.io.formats.xml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-2'),
  ('pandas.io.gbq',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-2'),
  ('pandas.io.html',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-2'),
  ('pandas.io.json',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.json._json',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-2'),
  ('pandas.io.json._normalize',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-2'),
  ('pandas.io.json._table_schema',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-2'),
  ('pandas.io.orc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-2'),
  ('pandas.io.parquet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-2'),
  ('pandas.io.parsers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.base_parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.python_parser',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-2'),
  ('pandas.io.parsers.readers',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-2'),
  ('pandas.io.pickle',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-2'),
  ('pandas.io.pytables',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-2'),
  ('pandas.io.sas',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas7bdat',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_constants',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sas_xport',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-2'),
  ('pandas.io.sas.sasreader',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-2'),
  ('pandas.io.spss',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-2'),
  ('pandas.io.sql',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-2'),
  ('pandas.io.stata',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-2'),
  ('pandas.io.xml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-2'),
  ('pandas.plotting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-2'),
  ('pandas.plotting._core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-2'),
  ('pandas.plotting._misc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-2'),
  ('pandas.testing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-2'),
  ('pandas.tseries',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-2'),
  ('pandas.tseries.api',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-2'),
  ('pandas.tseries.frequencies',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-2'),
  ('pandas.tseries.holiday',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE-2'),
  ('pandas.tseries.offsets',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-2'),
  ('pandas.util',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-2'),
  ('pandas.util._decorators',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-2'),
  ('pandas.util._exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-2'),
  ('pandas.util._print_versions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-2'),
  ('pandas.util._tester',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-2'),
  ('pandas.util._validators',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-2'),
  ('pandas.util.version',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-2'),
  ('pathlib', 'D:\\Python\\Python310\\lib\\pathlib.py', 'PYMODULE-2'),
  ('pdb', 'D:\\Python\\Python310\\lib\\pdb.py', 'PYMODULE-2'),
  ('pickle', 'D:\\Python\\Python310\\lib\\pickle.py', 'PYMODULE-2'),
  ('pickletools', 'D:\\Python\\Python310\\lib\\pickletools.py', 'PYMODULE-2'),
  ('pkgutil', 'D:\\Python\\Python310\\lib\\pkgutil.py', 'PYMODULE-2'),
  ('platform', 'D:\\Python\\Python310\\lib\\platform.py', 'PYMODULE-2'),
  ('pprint', 'D:\\Python\\Python310\\lib\\pprint.py', 'PYMODULE-2'),
  ('py_compile', 'D:\\Python\\Python310\\lib\\py_compile.py', 'PYMODULE-2'),
  ('pydoc', 'D:\\Python\\Python310\\lib\\pydoc.py', 'PYMODULE-2'),
  ('pydoc_data',
   'D:\\Python\\Python310\\lib\\pydoc_data\\__init__.py',
   'PYMODULE-2'),
  ('pydoc_data.topics',
   'D:\\Python\\Python310\\lib\\pydoc_data\\topics.py',
   'PYMODULE-2'),
  ('pytz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-2'),
  ('pytz.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-2'),
  ('pytz.lazy',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-2'),
  ('pytz.tzfile',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-2'),
  ('pytz.tzinfo',
   'D:\\code\\OF\\venv\\lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-2'),
  ('queue', 'D:\\Python\\Python310\\lib\\queue.py', 'PYMODULE-2'),
  ('quopri', 'D:\\Python\\Python310\\lib\\quopri.py', 'PYMODULE-2'),
  ('random', 'D:\\Python\\Python310\\lib\\random.py', 'PYMODULE-2'),
  ('rapidfuzz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\__init__.py',
   'PYMODULE-2'),
  ('rapidfuzz._common_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\_common_py.py',
   'PYMODULE-2'),
  ('rapidfuzz._feature_detector',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\_feature_detector.py',
   'PYMODULE-2'),
  ('rapidfuzz._utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\_utils.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\__init__.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.DamerauLevenshtein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\DamerauLevenshtein.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.DamerauLevenshtein_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\DamerauLevenshtein_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Hamming',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Hamming.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Hamming_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Hamming_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Indel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Indel.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Indel_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Indel_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Jaro',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Jaro.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.JaroWinkler',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\JaroWinkler.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.JaroWinkler_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\JaroWinkler_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Jaro_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Jaro_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.LCSseq',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\LCSseq.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.LCSseq_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\LCSseq_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Levenshtein',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Levenshtein.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Levenshtein_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Levenshtein_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.OSA',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\OSA.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.OSA_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\OSA_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Postfix',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Postfix.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Postfix_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Postfix_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Prefix',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Prefix.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.Prefix_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\Prefix_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance._initialize',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\_initialize.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance._initialize_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\_initialize_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.distance.metrics_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\distance\\metrics_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.fuzz',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\fuzz.py',
   'PYMODULE-2'),
  ('rapidfuzz.fuzz_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\fuzz_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.process',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\process.py',
   'PYMODULE-2'),
  ('rapidfuzz.process_cpp',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\process_cpp.py',
   'PYMODULE-2'),
  ('rapidfuzz.process_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\process_py.py',
   'PYMODULE-2'),
  ('rapidfuzz.utils',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\utils.py',
   'PYMODULE-2'),
  ('rapidfuzz.utils_py',
   'D:\\code\\OF\\venv\\lib\\site-packages\\rapidfuzz\\utils_py.py',
   'PYMODULE-2'),
  ('runpy', 'D:\\Python\\Python310\\lib\\runpy.py', 'PYMODULE-2'),
  ('secrets', 'D:\\Python\\Python310\\lib\\secrets.py', 'PYMODULE-2'),
  ('selectors', 'D:\\Python\\Python310\\lib\\selectors.py', 'PYMODULE-2'),
  ('shiboken6',
   'D:\\code\\OF\\venv\\lib\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE-2'),
  ('shlex', 'D:\\Python\\Python310\\lib\\shlex.py', 'PYMODULE-2'),
  ('shutil', 'D:\\Python\\Python310\\lib\\shutil.py', 'PYMODULE-2'),
  ('signal', 'D:\\Python\\Python310\\lib\\signal.py', 'PYMODULE-2'),
  ('six', 'D:\\code\\OF\\venv\\lib\\site-packages\\six.py', 'PYMODULE-2'),
  ('smtplib', 'D:\\Python\\Python310\\lib\\smtplib.py', 'PYMODULE-2'),
  ('socket', 'D:\\Python\\Python310\\lib\\socket.py', 'PYMODULE-2'),
  ('socketserver', 'D:\\Python\\Python310\\lib\\socketserver.py', 'PYMODULE-2'),
  ('sqlite3', 'D:\\Python\\Python310\\lib\\sqlite3\\__init__.py', 'PYMODULE-2'),
  ('sqlite3.dbapi2',
   'D:\\Python\\Python310\\lib\\sqlite3\\dbapi2.py',
   'PYMODULE-2'),
  ('sqlite3.dump',
   'D:\\Python\\Python310\\lib\\sqlite3\\dump.py',
   'PYMODULE-2'),
  ('src', 'd:\\code\\OF\\src\\__init__.py', 'PYMODULE-2'),
  ('src.config', 'd:\\code\\OF\\src\\config\\__init__.py', 'PYMODULE-2'),
  ('src.config.config_manager',
   'd:\\code\\OF\\src\\config\\config_manager.py',
   'PYMODULE-2'),
  ('src.core', 'd:\\code\\OF\\src\\core\\__init__.py', 'PYMODULE-2'),
  ('src.core.app_launcher',
   'd:\\code\\OF\\src\\core\\app_launcher.py',
   'PYMODULE-2'),
  ('src.core.fast_launcher',
   'd:\\code\\OF\\src\\core\\fast_launcher.py',
   'PYMODULE-2'),
  ('src.models', 'd:\\code\\OF\\src\\models\\__init__.py', 'PYMODULE-2'),
  ('src.models.surgery_record',
   'd:\\code\\OF\\src\\models\\surgery_record.py',
   'PYMODULE-2'),
  ('src.services', 'd:\\code\\OF\\src\\services\\__init__.py', 'PYMODULE-2'),
  ('src.services.data_import_service',
   'd:\\code\\OF\\src\\services\\data_import_service.py',
   'PYMODULE-2'),
  ('src.services.export_service',
   'd:\\code\\OF\\src\\services\\export_service.py',
   'PYMODULE-2'),
  ('src.services.matching_service',
   'd:\\code\\OF\\src\\services\\matching_service.py',
   'PYMODULE-2'),
  ('src.ui', 'd:\\code\\OF\\src\\ui\\__init__.py', 'PYMODULE-2'),
  ('src.ui.about_dialog',
   'd:\\code\\OF\\src\\ui\\about_dialog.py',
   'PYMODULE-2'),
  ('src.ui.content_manager',
   'd:\\code\\OF\\src\\ui\\content_manager.py',
   'PYMODULE-2'),
  ('src.ui.file_import_widget',
   'd:\\code\\OF\\src\\ui\\file_import_widget.py',
   'PYMODULE-2'),
  ('src.ui.help_dialog', 'd:\\code\\OF\\src\\ui\\help_dialog.py', 'PYMODULE-2'),
  ('src.ui.main_window', 'd:\\code\\OF\\src\\ui\\main_window.py', 'PYMODULE-2'),
  ('src.ui.matching_config_widget',
   'd:\\code\\OF\\src\\ui\\matching_config_widget.py',
   'PYMODULE-2'),
  ('src.ui.result_display_widget',
   'd:\\code\\OF\\src\\ui\\result_display_widget.py',
   'PYMODULE-2'),
  ('src.ui.stat_card_widget',
   'd:\\code\\OF\\src\\ui\\stat_card_widget.py',
   'PYMODULE-2'),
  ('src.ui.styles', 'd:\\code\\OF\\src\\ui\\styles.py', 'PYMODULE-2'),
  ('src.utils', 'd:\\code\\OF\\src\\utils\\__init__.py', 'PYMODULE-2'),
  ('src.utils.logger', 'd:\\code\\OF\\src\\utils\\logger.py', 'PYMODULE-2'),
  ('ssl', 'D:\\Python\\Python310\\lib\\ssl.py', 'PYMODULE-2'),
  ('statistics', 'D:\\Python\\Python310\\lib\\statistics.py', 'PYMODULE-2'),
  ('string', 'D:\\Python\\Python310\\lib\\string.py', 'PYMODULE-2'),
  ('stringprep', 'D:\\Python\\Python310\\lib\\stringprep.py', 'PYMODULE-2'),
  ('subprocess', 'D:\\Python\\Python310\\lib\\subprocess.py', 'PYMODULE-2'),
  ('sysconfig', 'D:\\Python\\Python310\\lib\\sysconfig.py', 'PYMODULE-2'),
  ('tarfile', 'D:\\Python\\Python310\\lib\\tarfile.py', 'PYMODULE-2'),
  ('tempfile', 'D:\\Python\\Python310\\lib\\tempfile.py', 'PYMODULE-2'),
  ('textwrap', 'D:\\Python\\Python310\\lib\\textwrap.py', 'PYMODULE-2'),
  ('threading', 'D:\\Python\\Python310\\lib\\threading.py', 'PYMODULE-2'),
  ('token', 'D:\\Python\\Python310\\lib\\token.py', 'PYMODULE-2'),
  ('tokenize', 'D:\\Python\\Python310\\lib\\tokenize.py', 'PYMODULE-2'),
  ('tracemalloc', 'D:\\Python\\Python310\\lib\\tracemalloc.py', 'PYMODULE-2'),
  ('tty', 'D:\\Python\\Python310\\lib\\tty.py', 'PYMODULE-2'),
  ('typing', 'D:\\Python\\Python310\\lib\\typing.py', 'PYMODULE-2'),
  ('unittest',
   'D:\\Python\\Python310\\lib\\unittest\\__init__.py',
   'PYMODULE-2'),
  ('unittest._log',
   'D:\\Python\\Python310\\lib\\unittest\\_log.py',
   'PYMODULE-2'),
  ('unittest.async_case',
   'D:\\Python\\Python310\\lib\\unittest\\async_case.py',
   'PYMODULE-2'),
  ('unittest.case',
   'D:\\Python\\Python310\\lib\\unittest\\case.py',
   'PYMODULE-2'),
  ('unittest.loader',
   'D:\\Python\\Python310\\lib\\unittest\\loader.py',
   'PYMODULE-2'),
  ('unittest.main',
   'D:\\Python\\Python310\\lib\\unittest\\main.py',
   'PYMODULE-2'),
  ('unittest.result',
   'D:\\Python\\Python310\\lib\\unittest\\result.py',
   'PYMODULE-2'),
  ('unittest.runner',
   'D:\\Python\\Python310\\lib\\unittest\\runner.py',
   'PYMODULE-2'),
  ('unittest.signals',
   'D:\\Python\\Python310\\lib\\unittest\\signals.py',
   'PYMODULE-2'),
  ('unittest.suite',
   'D:\\Python\\Python310\\lib\\unittest\\suite.py',
   'PYMODULE-2'),
  ('unittest.util',
   'D:\\Python\\Python310\\lib\\unittest\\util.py',
   'PYMODULE-2'),
  ('urllib', 'D:\\Python\\Python310\\lib\\urllib\\__init__.py', 'PYMODULE-2'),
  ('urllib.error',
   'D:\\Python\\Python310\\lib\\urllib\\error.py',
   'PYMODULE-2'),
  ('urllib.parse',
   'D:\\Python\\Python310\\lib\\urllib\\parse.py',
   'PYMODULE-2'),
  ('urllib.request',
   'D:\\Python\\Python310\\lib\\urllib\\request.py',
   'PYMODULE-2'),
  ('urllib.response',
   'D:\\Python\\Python310\\lib\\urllib\\response.py',
   'PYMODULE-2'),
  ('uu', 'D:\\Python\\Python310\\lib\\uu.py', 'PYMODULE-2'),
  ('uuid', 'D:\\Python\\Python310\\lib\\uuid.py', 'PYMODULE-2'),
  ('webbrowser', 'D:\\Python\\Python310\\lib\\webbrowser.py', 'PYMODULE-2'),
  ('xlrd',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\__init__.py',
   'PYMODULE-2'),
  ('xlrd.biffh',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\biffh.py',
   'PYMODULE-2'),
  ('xlrd.book',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\book.py',
   'PYMODULE-2'),
  ('xlrd.compdoc',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\compdoc.py',
   'PYMODULE-2'),
  ('xlrd.formatting',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\formatting.py',
   'PYMODULE-2'),
  ('xlrd.formula',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\formula.py',
   'PYMODULE-2'),
  ('xlrd.info',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\info.py',
   'PYMODULE-2'),
  ('xlrd.sheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\sheet.py',
   'PYMODULE-2'),
  ('xlrd.timemachine',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\timemachine.py',
   'PYMODULE-2'),
  ('xlrd.xldate',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlrd\\xldate.py',
   'PYMODULE-2'),
  ('xlsxwriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\__init__.py',
   'PYMODULE-2'),
  ('xlsxwriter.app',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\app.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_area',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_area.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_bar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_bar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_column',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_column.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_doughnut',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_doughnut.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_line',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_line.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_pie',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_pie.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_radar',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_radar.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_scatter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_scatter.py',
   'PYMODULE-2'),
  ('xlsxwriter.chart_stock',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chart_stock.py',
   'PYMODULE-2'),
  ('xlsxwriter.chartsheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\chartsheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.color',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\color.py',
   'PYMODULE-2'),
  ('xlsxwriter.comments',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\comments.py',
   'PYMODULE-2'),
  ('xlsxwriter.contenttypes',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\contenttypes.py',
   'PYMODULE-2'),
  ('xlsxwriter.core',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\core.py',
   'PYMODULE-2'),
  ('xlsxwriter.custom',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\custom.py',
   'PYMODULE-2'),
  ('xlsxwriter.drawing',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\drawing.py',
   'PYMODULE-2'),
  ('xlsxwriter.exceptions',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\exceptions.py',
   'PYMODULE-2'),
  ('xlsxwriter.feature_property_bag',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\feature_property_bag.py',
   'PYMODULE-2'),
  ('xlsxwriter.format',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\format.py',
   'PYMODULE-2'),
  ('xlsxwriter.image',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\image.py',
   'PYMODULE-2'),
  ('xlsxwriter.metadata',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\metadata.py',
   'PYMODULE-2'),
  ('xlsxwriter.packager',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\packager.py',
   'PYMODULE-2'),
  ('xlsxwriter.relationships',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\relationships.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_rel',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value_rel.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_structure',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value_structure.py',
   'PYMODULE-2'),
  ('xlsxwriter.rich_value_types',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\rich_value_types.py',
   'PYMODULE-2'),
  ('xlsxwriter.shape',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\shape.py',
   'PYMODULE-2'),
  ('xlsxwriter.sharedstrings',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\sharedstrings.py',
   'PYMODULE-2'),
  ('xlsxwriter.styles',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\styles.py',
   'PYMODULE-2'),
  ('xlsxwriter.table',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\table.py',
   'PYMODULE-2'),
  ('xlsxwriter.theme',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\theme.py',
   'PYMODULE-2'),
  ('xlsxwriter.url',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\url.py',
   'PYMODULE-2'),
  ('xlsxwriter.utility',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\utility.py',
   'PYMODULE-2'),
  ('xlsxwriter.vml',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\vml.py',
   'PYMODULE-2'),
  ('xlsxwriter.workbook',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\workbook.py',
   'PYMODULE-2'),
  ('xlsxwriter.worksheet',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\worksheet.py',
   'PYMODULE-2'),
  ('xlsxwriter.xmlwriter',
   'D:\\code\\OF\\venv\\lib\\site-packages\\xlsxwriter\\xmlwriter.py',
   'PYMODULE-2'),
  ('xml', 'D:\\Python\\Python310\\lib\\xml\\__init__.py', 'PYMODULE-2'),
  ('xml.dom',
   'D:\\Python\\Python310\\lib\\xml\\dom\\__init__.py',
   'PYMODULE-2'),
  ('xml.dom.NodeFilter',
   'D:\\Python\\Python310\\lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-2'),
  ('xml.dom.domreg',
   'D:\\Python\\Python310\\lib\\xml\\dom\\domreg.py',
   'PYMODULE-2'),
  ('xml.dom.expatbuilder',
   'D:\\Python\\Python310\\lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-2'),
  ('xml.dom.minicompat',
   'D:\\Python\\Python310\\lib\\xml\\dom\\minicompat.py',
   'PYMODULE-2'),
  ('xml.dom.minidom',
   'D:\\Python\\Python310\\lib\\xml\\dom\\minidom.py',
   'PYMODULE-2'),
  ('xml.dom.pulldom',
   'D:\\Python\\Python310\\lib\\xml\\dom\\pulldom.py',
   'PYMODULE-2'),
  ('xml.dom.xmlbuilder',
   'D:\\Python\\Python310\\lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-2'),
  ('xml.etree',
   'D:\\Python\\Python310\\lib\\xml\\etree\\__init__.py',
   'PYMODULE-2'),
  ('xml.etree.ElementInclude',
   'D:\\Python\\Python310\\lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-2'),
  ('xml.etree.ElementPath',
   'D:\\Python\\Python310\\lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-2'),
  ('xml.etree.ElementTree',
   'D:\\Python\\Python310\\lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-2'),
  ('xml.etree.cElementTree',
   'D:\\Python\\Python310\\lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-2'),
  ('xml.parsers',
   'D:\\Python\\Python310\\lib\\xml\\parsers\\__init__.py',
   'PYMODULE-2'),
  ('xml.parsers.expat',
   'D:\\Python\\Python310\\lib\\xml\\parsers\\expat.py',
   'PYMODULE-2'),
  ('xml.sax',
   'D:\\Python\\Python310\\lib\\xml\\sax\\__init__.py',
   'PYMODULE-2'),
  ('xml.sax._exceptions',
   'D:\\Python\\Python310\\lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-2'),
  ('xml.sax.expatreader',
   'D:\\Python\\Python310\\lib\\xml\\sax\\expatreader.py',
   'PYMODULE-2'),
  ('xml.sax.handler',
   'D:\\Python\\Python310\\lib\\xml\\sax\\handler.py',
   'PYMODULE-2'),
  ('xml.sax.saxutils',
   'D:\\Python\\Python310\\lib\\xml\\sax\\saxutils.py',
   'PYMODULE-2'),
  ('xml.sax.xmlreader',
   'D:\\Python\\Python310\\lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-2'),
  ('xmlrpc', 'D:\\Python\\Python310\\lib\\xmlrpc\\__init__.py', 'PYMODULE-2'),
  ('xmlrpc.client',
   'D:\\Python\\Python310\\lib\\xmlrpc\\client.py',
   'PYMODULE-2'),
  ('zipfile', 'D:\\Python\\Python310\\lib\\zipfile.py', 'PYMODULE-2'),
  ('zipimport', 'D:\\Python\\Python310\\lib\\zipimport.py', 'PYMODULE-2')])
