#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内容管理模块
统一管理应用中的文本内容，避免重复定义
"""


class ContentManager:
    """内容管理器 - 统一管理应用文本内容"""
    
    # 应用基本信息
    APP_NAME = "手术费用匹配系统"
    APP_NAME_EN = "Surgery Fee Matching System"
    APP_VERSION = "1.0.0 (Fast Edition)"
    APP_DESCRIPTION_SHORT = "专为医院手术费用数据匹配设计的智能分析工具"
    
    # 开发团队信息
    TEAM_INFO = {
        "developer": "医院信息科",
        "copyright": "© 2024 医院信息科. 保留所有权利.",
        "usage_note": "本软件仅供内部使用，请勿外传。"
    }
    
    # 联系方式
    CONTACT_INFO = [
        ("📧", "技术支持", "<EMAIL>", "mailto:<EMAIL>"),
        ("🌐", "官方网站", "www.hospital-system.com", "https://www.hospital-system.com"),
        ("📱", "技术交流", "QQ群: 123456789", None),
        ("📞", "服务热线", "400-123-4567", None),
        ("📍", "公司地址", "北京市朝阳区医疗科技园区", None),
    ]
    
    # 系统要求
    SYSTEM_REQUIREMENTS = {
        "os_support": [
            ("Windows 10", "64位", "推荐"),
            ("Windows 11", "64位", "推荐"),
            ("Windows 7", "64位", "基本支持"),
        ],
        "hardware": [
            ("处理器", "双核 2.0GHz", "四核 2.5GHz 或更高"),
            ("内存", "4GB RAM", "8GB RAM 或更多"),
            ("硬盘", "100MB 可用空间", "SSD 硬盘（更快启动）"),
            ("显示器", "1024×768", "1920×1080 或更高"),
        ],
        "notes": [
            "不支持32位操作系统",
            "Windows 7需要安装最新更新",
            "建议关闭杀毒软件的实时扫描以提升性能"
        ]
    }
    
    # 技术架构信息
    TECH_STACK = [
        ("开发语言", "Python 3.10+"),
        ("UI框架", "PySide6 (Qt6)"),
        ("数据处理", "Pandas, NumPy"),
        ("模糊匹配", "RapidFuzz / FuzzyWuzzy"),
        ("文件处理", "OpenPyXL, XlsxWriter"),
        ("打包工具", "PyInstaller"),
    ]
    
    @classmethod
    def get_app_description_full(cls):
        """获取完整的应用描述"""
        return f"""
        <b>{cls.APP_DESCRIPTION_SHORT}</b><br><br>
        本系统采用先进的模糊匹配算法和智能数据处理技术，
        能够高效、准确地完成不同数据源之间的记录匹配工作。
        """
    
    @classmethod
    def get_key_features(cls):
        """获取核心特性列表"""
        return [
            "🚀 快速启动：多线程预加载，1-3秒极速启动",
            "🎯 精准匹配：智能算法，匹配准确率高达95%以上",
            "📊 数据兼容：支持Excel、CSV等多种数据格式",
            "🎨 现代界面：美观易用的现代化用户界面",
            "🛡️ 稳定可靠：完善的错误处理和日志记录机制"
        ]
    
    @classmethod
    def get_system_requirements_html(cls):
        """获取系统要求的HTML格式"""
        html = """
        <h3>💻 系统要求</h3>
        <p><b>操作系统：</b>Windows 7/10/11 (64位)</p>
        <p><b>内存：</b>4GB以上推荐</p>
        <p><b>硬盘：</b>100MB可用空间</p>
        <p><b>运行时：</b>已内置Python运行时，无需额外安装</p>
        """
        return html
    
    @classmethod
    def get_contact_html(cls):
        """获取联系方式的HTML格式"""
        html = f"""
        <h3>📞 联系我们</h3>
        <p><b>技术支持：</b><EMAIL></p>
        <p><b>服务热线：</b>400-123-4567</p>
        <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
        <p style="text-align: center; font-size: 12px; color: #888;">
        {cls.TEAM_INFO['copyright']}<br>
        {cls.TEAM_INFO['usage_note']}
        </p>
        """
        return html

    @classmethod
    def get_detailed_requirements_html(cls):
        """获取详细系统要求的HTML格式（用于帮助页面）"""
        # 构建硬件要求表格
        hardware_rows = ""
        for component, minimum, recommended in cls.SYSTEM_REQUIREMENTS["hardware"]:
            hardware_rows += f"""
            <tr>
                <td style="padding: 10px; border: 1px solid #ddd;">{component}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">{minimum}</td>
                <td style="padding: 10px; border: 1px solid #ddd;">{recommended}</td>
            </tr>"""

        # 构建操作系统支持列表
        os_list = ""
        for os_name, arch, status in cls.SYSTEM_REQUIREMENTS["os_support"]:
            os_list += f"<li><b>{os_name}</b> ({arch}) - {status}</li>"

        # 构建注意事项列表
        notes_list = ""
        for note in cls.SYSTEM_REQUIREMENTS["notes"]:
            notes_list += f"<li>{note}</li>"

        html = f"""
        <h1 style="color: #1976D2; border-bottom: 2px solid #e3f2fd; padding-bottom: 10px;">
        💻 系统安装要求
        </h1>

        <h2 style="color: #2196F3;">硬件要求</h2>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <tr style="background: #f5f5f5;">
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">组件</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">最低要求</th>
                <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">推荐配置</th>
            </tr>
            {hardware_rows}
        </table>

        <h2 style="color: #2196F3;">操作系统支持</h2>
        <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #2196F3; margin: 15px 0;">
            <h3>✅ 支持的系统</h3>
            <ul>
                {os_list}
            </ul>
        </div>

        <div style="background: #fff3e0; padding: 15px; border-left: 4px solid #ff9800; margin: 15px 0;">
            <h3>⚠️ 注意事项</h3>
            <ul>
                {notes_list}
            </ul>
        </div>

        <h2 style="color: #2196F3;">软件依赖</h2>
        <p>本系统为独立可执行程序，<b>无需安装Python或其他依赖</b>，所有必要组件已内置。</p>

        <h3>可选组件</h3>
        <ul style="line-height: 1.8;">
            <li><b>Microsoft Office</b>：用于直接打开Excel文件（可选）</li>
            <li><b>WPS Office</b>：Excel文件的替代查看工具（可选）</li>
            <li><b>PDF阅读器</b>：查看帮助文档（可选）</li>
        </ul>

        <h2 style="color: #2196F3;">网络要求</h2>
        <div style="background: #e8f5e8; padding: 15px; border-left: 4px solid #4caf50; margin: 15px 0;">
            <p><b>✅ 无网络要求</b></p>
            <p>系统完全离线运行，不需要网络连接，确保数据安全。</p>
        </div>
        """
        return html
