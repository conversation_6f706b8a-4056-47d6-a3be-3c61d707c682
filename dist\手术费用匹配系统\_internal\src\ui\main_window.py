"""
主窗口界面 - 模块化重构版本
使用PySide6创建现代化的主界面
"""
import sys
import logging
from typing import List, Optional

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTabWidget, QPushButton, QSplitter, QFileDialog, QMessageBox,
    QTableWidget, QTableWidgetItem
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

from .styles import ModernStyle
from .file_import_widget import FileImportWidget
from .matching_config_widget import MatchingConfigWidget
from .result_display_widget import ResultDisplayWidget
from ..services.data_import_service import DataImportService
from ..services.matching_service import SurgeryMatchingService
from ..models.surgery_record import SurgeryRecordTable1, SurgeryRecordTable2, MatchedSurgeryRecord


class MainWindow(QMainWindow):
    """主窗口 - 模块化版本"""

    def __init__(self):
        super().__init__()
        # 数据存储
        self.table1_records = []
        self.table2_records = []
        self.matched_records = []

        # 服务实例
        self.data_import_service = DataImportService()
        self.matching_service = None

        # 初始化界面
        self._init_ui()
        self._setup_logging()
        self._connect_signals()

        # 确保按钮初始状态正确
        self._update_button_states()

    def _init_ui(self):
        """初始化界面"""
        self.setWindowTitle("手术费用匹配系统")
        self.setGeometry(100, 100, 1200, 800)

        # 设置窗口图标
        self._set_window_icon()

        # 应用样式
        self.setStyleSheet(ModernStyle.get_main_style())

        # 创建菜单栏
        self._create_menu_bar()

        # 创建对话框（延迟创建）
        self.about_dialog = None
        self.help_dialog = None

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 创建主布局
        main_layout = QHBoxLayout()

        # 左侧面板
        left_panel = self._create_left_panel()

        # 右侧面板
        right_panel = self._create_right_panel()

        # 分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)

        # 设置合理的宽度范围，让内容自适应
        left_panel.setMinimumWidth(280)  # 确保内容能完整显示
        left_panel.setMaximumWidth(400)  # 限制最大宽度

        # 设置初始比例：左侧320px，右侧880px
        splitter.setSizes([320, 880])

        main_layout.addWidget(splitter)
        central_widget.setLayout(main_layout)

        # 状态栏
        self.statusBar().showMessage("就绪")
        self.statusBar().setStyleSheet(ModernStyle.get_status_bar_style())

    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        layout = QVBoxLayout()

        # 创建选项卡
        tab_widget = QTabWidget()

        # 文件导入选项卡
        self.import_widget = FileImportWidget()
        tab_widget.addTab(self.import_widget, "📁 数据导入")

        # 匹配配置选项卡
        self.config_widget = MatchingConfigWidget()
        tab_widget.addTab(self.config_widget, "⚙️ 匹配配置")

        layout.addWidget(tab_widget)

        # 操作按钮
        button_layout = QVBoxLayout()

        self.match_btn = QPushButton("🔗 开始匹配")
        self.match_btn.clicked.connect(self._start_matching)
        self.match_btn.setEnabled(False)
        self.match_btn.setMinimumHeight(32)
        self.match_btn.setToolTip("需要导入两个表的数据后才能开始匹配")

        self.export_btn = QPushButton("📤 导出结果")
        self.export_btn.clicked.connect(self._export_results)
        self.export_btn.setEnabled(False)
        self.export_btn.setMinimumHeight(32)
        self.export_btn.setToolTip("需要完成匹配后才能导出结果")

        button_layout.addWidget(self.match_btn)
        button_layout.addWidget(self.export_btn)

        layout.addLayout(button_layout)
        layout.addStretch()

        panel.setLayout(layout)
        return panel

    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        panel = QWidget()
        layout = QVBoxLayout()

        # 主选项卡
        self.main_tab_widget = QTabWidget()

        # 数据预览选项卡
        preview_widget = self._create_preview_widget()
        self.main_tab_widget.addTab(preview_widget, "数据预览")

        # 匹配结果选项卡
        self.result_widget = ResultDisplayWidget()
        self.main_tab_widget.addTab(self.result_widget, "匹配结果")

        layout.addWidget(self.main_tab_widget)
        panel.setLayout(layout)

        return panel

    def _create_preview_widget(self) -> QWidget:
        """创建预览组件"""
        widget = QWidget()
        layout = QVBoxLayout()

        # 预览选项卡
        self.preview_tab_widget = QTabWidget()

        # 手术表预览
        self.table1_preview = QTableWidget()
        self._setup_empty_table_preview(self.table1_preview)
        self.preview_tab_widget.addTab(self.table1_preview, "手术表预览")

        # 手术费表预览
        self.table2_preview = QTableWidget()
        self._setup_empty_table_preview(self.table2_preview)
        self.preview_tab_widget.addTab(self.table2_preview, "手术费表预览")

        layout.addWidget(self.preview_tab_widget)
        widget.setLayout(layout)

        return widget

    def _setup_empty_table_preview(self, table_widget: QTableWidget):
        """设置空的表格预览"""
        # 初始化为空表格
        table_widget.setRowCount(0)
        table_widget.setColumnCount(0)

        # 设置表格样式
        table_widget.setAlternatingRowColors(True)
        table_widget.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)

        # 设置列宽自适应
        table_widget.horizontalHeader().setStretchLastSection(True)

    def _connect_signals(self):
        """连接信号"""
        # 文件导入信号
        self.import_widget.file_imported.connect(self._on_file_imported)
        self.import_widget.preview_requested.connect(self._on_preview_requested)

    def _setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

    def _update_button_states(self):
        """更新按钮状态"""
        # 开始匹配按钮：需要两个表都有数据
        can_match = bool(self.table1_records and self.table2_records)
        self.match_btn.setEnabled(can_match)

        # 导出按钮：需要有匹配结果
        can_export = bool(self.matched_records)
        self.export_btn.setEnabled(can_export)

        # 更新按钮提示信息
        if can_match:
            self.match_btn.setToolTip("点击开始匹配数据")
        else:
            self.match_btn.setToolTip("需要导入两个表的数据后才能开始匹配")

        if can_export:
            self.export_btn.setToolTip("点击导出匹配结果")
        else:
            self.export_btn.setToolTip("需要完成匹配后才能导出结果")

        # 更新状态栏
        self._update_status_bar()

    def _create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 导入数据
        import_action = file_menu.addAction("导入数据")
        import_action.setShortcut("Ctrl+O")
        import_action.triggered.connect(self._show_import_dialog)

        # 导出结果
        export_action = file_menu.addAction("导出结果")
        export_action.setShortcut("Ctrl+S")
        export_action.triggered.connect(self._show_export_dialog)

        file_menu.addSeparator()

        # 退出
        exit_action = file_menu.addAction("退出")
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 匹配配置
        config_action = tools_menu.addAction("匹配配置")
        config_action.triggered.connect(self._show_config_dialog)

        # 清空数据
        clear_action = tools_menu.addAction("清空数据")
        clear_action.triggered.connect(self._clear_all_data)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 使用说明
        help_action = help_menu.addAction("使用说明")
        help_action.setShortcut("F1")
        help_action.triggered.connect(self._show_help)

        help_menu.addSeparator()

        # 关于
        about_action = help_menu.addAction("关于")
        about_action.triggered.connect(self._show_about)

    def _set_window_icon(self):
        """设置窗口图标 - 兼容打包后的exe文件"""
        try:
            import sys
            import os
            from pathlib import Path

            # 获取资源路径（兼容打包后的exe）
            if getattr(sys, 'frozen', False):
                # 打包后的exe文件
                base_path = Path(sys._MEIPASS)
            else:
                # 开发环境
                base_path = Path(__file__).parent.parent.parent

            # 尝试多种图标路径
            icon_paths = [
                base_path / "src" / "logo" / "logo.ico",
                base_path / "src" / "logo" / "logo.png",
                Path("src") / "logo" / "logo.ico",
                Path("src") / "logo" / "logo.png",
            ]

            for icon_path in icon_paths:
                if icon_path.exists():
                    self.setWindowIcon(QIcon(str(icon_path)))
                    print(f"✅ 窗口图标加载成功: {icon_path}")
                    return

            print("⚠️ 未找到窗口图标文件")

        except Exception as e:
            print(f"⚠️ 窗口图标设置失败: {e}")

    def _show_import_dialog(self):
        """显示导入对话框"""
        from PySide6.QtWidgets import QFileDialog, QMessageBox

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择要导入的文件",
            "",
            "Excel文件 (*.xlsx *.xls);;CSV文件 (*.csv);;所有文件 (*.*)"
        )

        if file_path:
            QMessageBox.information(self, "导入文件",
                f"请使用左侧面板的导入功能来导入文件：\n{file_path}")

    def _show_export_dialog(self):
        """显示导出对话框"""
        from PySide6.QtWidgets import QMessageBox

        if not self.matched_records:
            QMessageBox.warning(self, "导出失败", "没有匹配结果可以导出，请先进行数据匹配。")
            return

        # 触发结果显示组件的导出功能
        self.result_display._export_results()

    def _show_config_dialog(self):
        """显示配置对话框"""
        from PySide6.QtWidgets import QMessageBox

        QMessageBox.information(self, "匹配配置",
            "请使用左侧面板的匹配配置功能来调整匹配参数。")

    def _clear_all_data(self):
        """清空所有数据"""
        # 清空表格数据
        self.table1_records = []
        self.table2_records = []
        self.matched_records = []

        # 更新显示
        self._clear_preview_table("table1")
        self._clear_preview_table("table2")
        self.result_display.clear_results()

        # 更新状态栏
        self._update_status_bar()

    def _show_help(self):
        """显示帮助对话框"""
        if self.help_dialog is None:
            from .help_dialog import HelpDialog
            self.help_dialog = HelpDialog(self)

        self.help_dialog.exec()

    def _show_about(self):
        """显示关于对话框"""
        if self.about_dialog is None:
            from .about_dialog import AboutDialog
            self.about_dialog = AboutDialog(self)

        self.about_dialog.exec()

    def _update_status_bar(self):
        """更新状态栏"""
        status_parts = []

        if self.table1_records:
            status_parts.append(f"🏥 手术表: {len(self.table1_records)}条")
        else:
            status_parts.append("🏥 手术表: 未导入")

        if self.table2_records:
            status_parts.append(f"� 手术费表: {len(self.table2_records)}条")
        else:
            status_parts.append("� 手术费表: 未导入")

        if self.matched_records:
            matched_count = len([r for r in self.matched_records if r.match_status.startswith("已匹配")])
            status_parts.append(f"🔗 匹配: {matched_count}/{len(self.matched_records)}条")

        if status_parts:
            status_message = " | ".join(status_parts)
        else:
            status_message = "🚀 就绪 - 请导入数据开始匹配"

        self.statusBar().showMessage(status_message)

    def _on_preview_requested(self, file_path: str, sheet_name: str, table_type: str):
        """处理预览请求"""
        try:
            df = self.data_import_service.preview_data(file_path, sheet_name, rows=10)
            if df is not None:
                self._update_preview_table(df, table_type)

                # 检测表格类型
                detected_type = self.data_import_service.detect_table_type(df)
                self.import_widget._add_formatted_status(f"{table_type}检测到表格类型: {detected_type}", "info")

                # 自动切换到数据预览选项卡
                self.main_tab_widget.setCurrentIndex(0)

                # 切换到对应的预览子选项卡
                if table_type == "table1":
                    self.preview_tab_widget.setCurrentIndex(0)
                else:
                    self.preview_tab_widget.setCurrentIndex(1)
            else:
                self._clear_preview_table(table_type)

        except Exception as e:
            self.import_widget._add_formatted_status(f"{table_type}预览失败: {str(e)}", "error")
            self._clear_preview_table(table_type)

    def _update_preview_table(self, df, table_type: str):
        """更新预览表格（显示实际数据内容）"""
        if table_type == "table1":
            preview_table = self.table1_preview
        else:
            preview_table = self.table2_preview

        # 设置表格结构（使用实际的列名）
        preview_table.setRowCount(len(df))
        preview_table.setColumnCount(len(df.columns))
        preview_table.setHorizontalHeaderLabels(df.columns.tolist())

        # 填充实际数据
        for row in range(len(df)):
            for col in range(len(df.columns)):
                value = str(df.iloc[row, col])
                preview_table.setItem(row, col, QTableWidgetItem(value))

        # 自动调整列宽
        preview_table.resizeColumnsToContents()

    def _clear_preview_table(self, table_type: str):
        """清空预览表格"""
        if table_type == "table1":
            preview_table = self.table1_preview
        else:
            preview_table = self.table2_preview

        # 完全清空表格
        preview_table.setRowCount(0)
        preview_table.setColumnCount(0)

    def _on_file_imported(self, file_path: str, table_type: str, sheet_name: str, records: list, errors: list):
        """文件导入完成回调"""
        try:
            if table_type == "table1":
                self.table1_records = records
                if errors:
                    self.import_widget._add_formatted_status(f"表一导入警告: {len(errors)}个问题", "warning")
            else:
                self.table2_records = records
                if errors:
                    self.import_widget._add_formatted_status(f"表二导入警告: {len(errors)}个问题", "warning")

            # 更新按钮状态和状态栏
            self._update_button_states()

            # 检查是否可以开始匹配
            if self.table1_records and self.table2_records:
                self.import_widget._add_formatted_status("✅ 两个表的数据都已导入，可以开始匹配", "success")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"导入处理失败: {str(e)}")

    def _start_matching(self):
        """开始匹配"""
        if not self.table1_records or not self.table2_records:
            QMessageBox.warning(self, "警告", "请先导入两个表的数据")
            return

        try:
            # 获取匹配配置
            config = self.config_widget.get_config()

            # 创建匹配服务
            self.matching_service = SurgeryMatchingService(
                name_threshold=config['name_threshold'],
                date_tolerance_days=config['date_tolerance_days'],
                admission_number_exact=config['admission_number_exact'],
                min_match_score=config['min_match_score']
            )

            # 执行匹配
            self.statusBar().showMessage("正在匹配数据...")

            self.matched_records = self.matching_service.match_records(
                self.table1_records,
                self.table2_records
            )

            # 显示结果
            self.result_widget.update_results(self.matched_records)

            # 更新按钮状态
            self._update_button_states()

            # 自动切换到匹配结果选项卡
            self.main_tab_widget.setCurrentIndex(1)

        except Exception as e:
            QMessageBox.critical(self, "错误", f"匹配失败: {str(e)}")

    def _export_results(self):
        """导出结果"""
        if not self.matched_records:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return

        # 选择导出文件
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出匹配结果",
            "手术费用匹配结果.xlsx",
            "Excel文件 (*.xlsx)"
        )

        if file_path:
            try:
                from ..services.export_service import ExportService
                export_service = ExportService()
                export_service.export_matched_results(self.matched_records, file_path)

                QMessageBox.information(self, "成功", f"结果已导出到: {file_path}")
                self.statusBar().showMessage(f"导出完成: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出失败: {str(e)}")

    def get_matched_records(self) -> List[MatchedSurgeryRecord]:
        """获取匹配记录"""
        return self.matched_records.copy()

    def get_statistics(self) -> dict:
        """获取统计信息"""
        return self.result_widget.get_statistics()

    def reset_data(self):
        """重置所有数据"""
        self.table1_records = []
        self.table2_records = []
        self.matched_records = []

        # 重置UI组件
        self.import_widget.reset_status()
        self.result_widget.clear_results()
        self._clear_preview_table("table1")
        self._clear_preview_table("table2")

        # 更新状态
        self._update_button_states()

        QMessageBox.information(self, "成功", "所有数据已重置")


def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序信息
    app.setApplicationName("手术费用匹配系统")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("医院信息系统")

    # 创建主窗口
    window = MainWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
